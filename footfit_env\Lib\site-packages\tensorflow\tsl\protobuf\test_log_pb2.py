# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: tensorflow/tsl/protobuf/test_log.proto
"""Generated protocol buffer code."""
from google.protobuf.internal import builder as _builder
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from google.protobuf import any_pb2 as google_dot_protobuf_dot_any__pb2
from google.protobuf import wrappers_pb2 as google_dot_protobuf_dot_wrappers__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n&tensorflow/tsl/protobuf/test_log.proto\x12\ntensorflow\x1a\x19google/protobuf/any.proto\x1a\x1egoogle/protobuf/wrappers.proto\"D\n\nEntryValue\x12\x16\n\x0c\x64ouble_value\x18\x01 \x01(\x01H\x00\x12\x16\n\x0cstring_value\x18\x02 \x01(\tH\x00\x42\x06\n\x04kind\"\x8c\x01\n\x0bMetricEntry\x12\x0c\n\x04name\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\x01\x12/\n\tmin_value\x18\x03 \x01(\x0b\x32\x1c.google.protobuf.DoubleValue\x12/\n\tmax_value\x18\x04 \x01(\x0b\x32\x1c.google.protobuf.DoubleValue\"\x8f\x02\n\x0e\x42\x65nchmarkEntry\x12\x0c\n\x04name\x18\x01 \x01(\t\x12\r\n\x05iters\x18\x02 \x01(\x03\x12\x10\n\x08\x63pu_time\x18\x03 \x01(\x01\x12\x11\n\twall_time\x18\x04 \x01(\x01\x12\x12\n\nthroughput\x18\x05 \x01(\x01\x12\x36\n\x06\x65xtras\x18\x06 \x03(\x0b\x32&.tensorflow.BenchmarkEntry.ExtrasEntry\x12(\n\x07metrics\x18\x07 \x03(\x0b\x32\x17.tensorflow.MetricEntry\x1a\x45\n\x0b\x45xtrasEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12%\n\x05value\x18\x02 \x01(\x0b\x32\x16.tensorflow.EntryValue:\x02\x38\x01\"=\n\x10\x42\x65nchmarkEntries\x12)\n\x05\x65ntry\x18\x01 \x03(\x0b\x32\x1a.tensorflow.BenchmarkEntry\"B\n\x12\x42uildConfiguration\x12\x0c\n\x04mode\x18\x01 \x01(\t\x12\x10\n\x08\x63\x63_flags\x18\x02 \x03(\t\x12\x0c\n\x04opts\x18\x03 \x03(\t\"f\n\x08\x43ommitId\x12\x14\n\nchangelist\x18\x01 \x01(\x03H\x00\x12\x0e\n\x04hash\x18\x02 \x01(\tH\x00\x12\x10\n\x08snapshot\x18\x03 \x01(\t\x12\x1a\n\x12pending_changelist\x18\x04 \x01(\x03\x42\x06\n\x04kind\"\xde\x01\n\x07\x43PUInfo\x12\x11\n\tnum_cores\x18\x01 \x01(\x03\x12\x19\n\x11num_cores_allowed\x18\x02 \x01(\x03\x12\x13\n\x0bmhz_per_cpu\x18\x03 \x01(\x01\x12\x10\n\x08\x63pu_info\x18\x04 \x01(\t\x12\x14\n\x0c\x63pu_governor\x18\x05 \x01(\t\x12\x36\n\ncache_size\x18\x06 \x03(\x0b\x32\".tensorflow.CPUInfo.CacheSizeEntry\x1a\x30\n\x0e\x43\x61\x63heSizeEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\x03:\x02\x38\x01\".\n\nMemoryInfo\x12\r\n\x05total\x18\x01 \x01(\x03\x12\x11\n\tavailable\x18\x02 \x01(\x03\"6\n\x07GPUInfo\x12\r\n\x05model\x18\x01 \x01(\t\x12\x0c\n\x04uuid\x18\x02 \x01(\t\x12\x0e\n\x06\x62us_id\x18\x03 \x01(\t\"p\n\x0cPlatformInfo\x12\x0c\n\x04\x62its\x18\x01 \x01(\t\x12\x0f\n\x07linkage\x18\x02 \x01(\t\x12\x0f\n\x07machine\x18\x03 \x01(\t\x12\x0f\n\x07release\x18\x04 \x01(\t\x12\x0e\n\x06system\x18\x05 \x01(\t\x12\x0f\n\x07version\x18\x06 \x01(\t\"e\n\x13\x41vailableDeviceInfo\x12\x0c\n\x04name\x18\x01 \x01(\t\x12\x0c\n\x04type\x18\x02 \x01(\t\x12\x14\n\x0cmemory_limit\x18\x03 \x01(\x03\x12\x1c\n\x14physical_description\x18\x04 \x01(\t\"\xb3\x02\n\x14MachineConfiguration\x12\x10\n\x08hostname\x18\x01 \x01(\t\x12\x19\n\x11serial_identifier\x18\x07 \x01(\t\x12/\n\rplatform_info\x18\x02 \x01(\x0b\x32\x18.tensorflow.PlatformInfo\x12%\n\x08\x63pu_info\x18\x03 \x01(\x0b\x32\x13.tensorflow.CPUInfo\x12)\n\x0b\x64\x65vice_info\x18\x04 \x03(\x0b\x32\x14.google.protobuf.Any\x12>\n\x15\x61vailable_device_info\x18\x05 \x03(\x0b\x32\x1f.tensorflow.AvailableDeviceInfo\x12+\n\x0bmemory_info\x18\x06 \x01(\x0b\x32\x16.tensorflow.MemoryInfo\"\x91\x01\n\x10RunConfiguration\x12\x10\n\x08\x61rgument\x18\x01 \x03(\t\x12;\n\x08\x65nv_vars\x18\x02 \x03(\x0b\x32).tensorflow.RunConfiguration.EnvVarsEntry\x1a.\n\x0c\x45nvVarsEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t:\x02\x38\x01\"\xd0\x04\n\x0bTestResults\x12\x0e\n\x06target\x18\x01 \x01(\t\x12-\n\x07\x65ntries\x18\x02 \x01(\x0b\x32\x1c.tensorflow.BenchmarkEntries\x12;\n\x13\x62uild_configuration\x18\x03 \x01(\x0b\x32\x1e.tensorflow.BuildConfiguration\x12\'\n\tcommit_id\x18\x04 \x01(\x0b\x32\x14.tensorflow.CommitId\x12\x12\n\nstart_time\x18\x05 \x01(\x03\x12\x10\n\x08run_time\x18\x06 \x01(\x01\x12?\n\x15machine_configuration\x18\x07 \x01(\x0b\x32 .tensorflow.MachineConfiguration\x12\x37\n\x11run_configuration\x18\x08 \x01(\x0b\x32\x1c.tensorflow.RunConfiguration\x12\x0c\n\x04name\x18\t \x01(\t\x12=\n\x0e\x62\x65nchmark_type\x18\n \x01(\x0e\x32%.tensorflow.TestResults.BenchmarkType\x12\x10\n\x08run_mode\x18\x0b \x01(\t\x12\x12\n\ntf_version\x18\x0c \x01(\t\"\x88\x01\n\rBenchmarkType\x12\x0b\n\x07UNKNOWN\x10\x00\x12\x16\n\x12\x43PP_MICROBENCHMARK\x10\x01\x12\x14\n\x10PYTHON_BENCHMARK\x10\x02\x12\x15\n\x11\x41NDROID_BENCHMARK\x10\x03\x12\x12\n\x0e\x45\x44GE_BENCHMARK\x10\x04\x12\x11\n\rIOS_BENCHMARK\x10\x05\x42\x31\n\x1borg.tensorflow.util.testlogB\rTestLogProtosP\x01\xf8\x01\x01\x62\x06proto3')

_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, globals())
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'tensorflow.tsl.protobuf.test_log_pb2', globals())
if _descriptor._USE_C_DESCRIPTORS == False:

  DESCRIPTOR._options = None
  DESCRIPTOR._serialized_options = b'\n\033org.tensorflow.util.testlogB\rTestLogProtosP\001\370\001\001'
  _BENCHMARKENTRY_EXTRASENTRY._options = None
  _BENCHMARKENTRY_EXTRASENTRY._serialized_options = b'8\001'
  _CPUINFO_CACHESIZEENTRY._options = None
  _CPUINFO_CACHESIZEENTRY._serialized_options = b'8\001'
  _RUNCONFIGURATION_ENVVARSENTRY._options = None
  _RUNCONFIGURATION_ENVVARSENTRY._serialized_options = b'8\001'
  _ENTRYVALUE._serialized_start=113
  _ENTRYVALUE._serialized_end=181
  _METRICENTRY._serialized_start=184
  _METRICENTRY._serialized_end=324
  _BENCHMARKENTRY._serialized_start=327
  _BENCHMARKENTRY._serialized_end=598
  _BENCHMARKENTRY_EXTRASENTRY._serialized_start=529
  _BENCHMARKENTRY_EXTRASENTRY._serialized_end=598
  _BENCHMARKENTRIES._serialized_start=600
  _BENCHMARKENTRIES._serialized_end=661
  _BUILDCONFIGURATION._serialized_start=663
  _BUILDCONFIGURATION._serialized_end=729
  _COMMITID._serialized_start=731
  _COMMITID._serialized_end=833
  _CPUINFO._serialized_start=836
  _CPUINFO._serialized_end=1058
  _CPUINFO_CACHESIZEENTRY._serialized_start=1010
  _CPUINFO_CACHESIZEENTRY._serialized_end=1058
  _MEMORYINFO._serialized_start=1060
  _MEMORYINFO._serialized_end=1106
  _GPUINFO._serialized_start=1108
  _GPUINFO._serialized_end=1162
  _PLATFORMINFO._serialized_start=1164
  _PLATFORMINFO._serialized_end=1276
  _AVAILABLEDEVICEINFO._serialized_start=1278
  _AVAILABLEDEVICEINFO._serialized_end=1379
  _MACHINECONFIGURATION._serialized_start=1382
  _MACHINECONFIGURATION._serialized_end=1689
  _RUNCONFIGURATION._serialized_start=1692
  _RUNCONFIGURATION._serialized_end=1837
  _RUNCONFIGURATION_ENVVARSENTRY._serialized_start=1791
  _RUNCONFIGURATION_ENVVARSENTRY._serialized_end=1837
  _TESTRESULTS._serialized_start=1840
  _TESTRESULTS._serialized_end=2432
  _TESTRESULTS_BENCHMARKTYPE._serialized_start=2296
  _TESTRESULTS_BENCHMARKTYPE._serialized_end=2432
# @@protoc_insertion_point(module_scope)
