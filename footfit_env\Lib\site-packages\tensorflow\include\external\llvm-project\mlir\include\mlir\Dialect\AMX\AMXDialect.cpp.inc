/*===- TableGen'erated file -------------------------------------*- C++ -*-===*\
|*                                                                            *|
|* Dialect Definitions                                                        *|
|*                                                                            *|
|* Automatically generated file, do not edit!                                 *|
|*                                                                            *|
\*===----------------------------------------------------------------------===*/

MLIR_DEFINE_EXPLICIT_TYPE_ID(::mlir::amx::AMXDialect)
namespace mlir {
namespace amx {

AMXDialect::AMXDialect(::mlir::MLIRContext *context)
    : ::mlir::Dialect(getDialectNamespace(), context, ::mlir::TypeID::get<AMXDialect>()) {
  
  initialize();
}

AMXDialect::~AMXDialect() = default;

} // namespace amx
} // namespace mlir
