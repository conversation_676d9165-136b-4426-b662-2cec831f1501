#!/usr/bin/env node

/**
 * FootFit CNN Training Pipeline Diagnostics
 * Comprehensive system check for training environment
 */

const fs = require('fs');
const path = require('path');

console.log('🔍 FOOTFIT CNN TRAINING DIAGNOSTICS');
console.log('=' .repeat(50));

let diagnosticResults = {
  tensorflow: false,
  imports: false,
  datasets: false,
  modelCreation: false,
  tensorOps: false,
  dependencies: false,
  permissions: false,
  memory: false,
  paths: false,
  minimalTest: false
};

// 1. TensorFlow.js Installation and Version
async function checkTensorFlow() {
  console.log('\n1️⃣ CHECKING TENSORFLOW.JS...');
  try {
    // Try browser version first (more compatible)
    let tf;
    try {
      tf = require('@tensorflow/tfjs');
      console.log(`   ✅ TensorFlow.js (browser) version: ${tf.version.tfjs}`);
      console.log(`   ✅ Backend: ${tf.getBackend()}`);
    } catch (browserError) {
      // Fallback to node version
      tf = require('@tensorflow/tfjs-node');
      console.log(`   ✅ TensorFlow.js (node) version: ${tf.version.tfjs}`);
      console.log(`   ✅ Backend: ${tf.getBackend()}`);
      console.log(`   ✅ Platform: ${tf.ENV.platform}`);
    }

    // Test basic tensor operation
    const testTensor = tf.tensor2d([[1, 2], [3, 4]]);
    console.log(`   ✅ Basic tensor creation: ${testTensor.shape}`);
    testTensor.dispose();

    diagnosticResults.tensorflow = true;
    return true;
  } catch (error) {
    console.log(`   ❌ TensorFlow.js error: ${error.message}`);
    return false;
  }
}

// 2. Node.js Module Imports
async function checkImports() {
  console.log('\n2️⃣ CHECKING MODULE IMPORTS...');
  const requiredModules = [
    '@tensorflow/tfjs',
    'fs',
    'path'
  ];
  
  let allImportsWork = true;
  
  for (const module of requiredModules) {
    try {
      require(module);
      console.log(`   ✅ ${module}: OK`);
    } catch (error) {
      console.log(`   ❌ ${module}: ${error.message}`);
      allImportsWork = false;
    }
  }
  
  diagnosticResults.imports = allImportsWork;
  return allImportsWork;
}

// 3. Dataset Files and Directory Structure
async function checkDatasets() {
  console.log('\n3️⃣ CHECKING DATASET STRUCTURE...');
  const datasetPath = path.join(__dirname, '..', 'datasets');
  
  try {
    // Check main dataset directory
    if (!fs.existsSync(datasetPath)) {
      console.log(`   ❌ Dataset directory not found: ${datasetPath}`);
      return false;
    }
    console.log(`   ✅ Dataset directory exists: ${datasetPath}`);
    
    // Check subdirectories with correct structure
    const requiredDirs = [
      { name: 'train', path: 'train/images' },
      { name: 'validation', path: 'validation/images' },
      { name: 'test', path: 'test/images' }
    ];

    for (const dir of requiredDirs) {
      const dirPath = path.join(datasetPath, dir.path);
      if (fs.existsSync(dirPath)) {
        const files = fs.readdirSync(dirPath);
        const imageFiles = files.filter(f => f.endsWith('.jpg') || f.endsWith('.png'));
        console.log(`   ✅ ${dir.name}: ${imageFiles.length} images`);
      } else {
        console.log(`   ❌ Missing directory: ${dir.path}`);
        return false;
      }
    }
    
    diagnosticResults.datasets = true;
    return true;
  } catch (error) {
    console.log(`   ❌ Dataset check error: ${error.message}`);
    return false;
  }
}

// 4. Model Architecture Creation
async function checkModelCreation() {
  console.log('\n4️⃣ CHECKING MODEL CREATION...');
  try {
    const tf = require('@tensorflow/tfjs');
    
    // Create simple CNN model (browser-compatible)
    const model = tf.sequential();

    model.add(tf.layers.conv2d({
      inputShape: [224, 224, 3],
      filters: 32,
      kernelSize: 3,
      activation: 'relu'
    }));
    model.add(tf.layers.maxPooling2d({ poolSize: 2 }));
    model.add(tf.layers.conv2d({
      filters: 64,
      kernelSize: 3,
      activation: 'relu'
    }));
    model.add(tf.layers.flatten());
    model.add(tf.layers.dense({ units: 128, activation: 'relu' }));
    model.add(tf.layers.dense({ units: 4 })); // foot measurements
    
    console.log(`   ✅ Model created successfully`);
    console.log(`   ✅ Total parameters: ${model.countParams()}`);
    
    // Test model compilation
    model.compile({
      optimizer: tf.train.adam(0.0005),
      loss: 'meanSquaredError',
      metrics: ['mae']
    });
    console.log(`   ✅ Model compiled successfully`);
    
    model.dispose();
    diagnosticResults.modelCreation = true;
    return true;
  } catch (error) {
    console.log(`   ❌ Model creation error: ${error.message}`);
    return false;
  }
}

// 5. Tensor Operations
async function checkTensorOps() {
  console.log('\n5️⃣ CHECKING TENSOR OPERATIONS...');
  try {
    const tf = require('@tensorflow/tfjs');
    
    // Test tensor creation
    const x = tf.randomNormal([10, 224, 224, 3]);
    const y = tf.randomNormal([10, 4]);
    console.log(`   ✅ Random tensors created: x=${x.shape}, y=${y.shape}`);
    
    // Test tensor operations
    const mean = tf.mean(x);
    console.log(`   ✅ Tensor operations work: mean=${await mean.data()}`);
    
    // Test memory cleanup
    x.dispose();
    y.dispose();
    mean.dispose();
    console.log(`   ✅ Tensor disposal works`);
    
    diagnosticResults.tensorOps = true;
    return true;
  } catch (error) {
    console.log(`   ❌ Tensor operations error: ${error.message}`);
    return false;
  }
}

// 6. Dependencies Check
async function checkDependencies() {
  console.log('\n6️⃣ CHECKING DEPENDENCIES...');
  try {
    const packagePath = path.join(__dirname, '..', 'package.json');
    if (fs.existsSync(packagePath)) {
      const packageJson = JSON.parse(fs.readFileSync(packagePath, 'utf8'));
      console.log(`   ✅ Package.json found`);
      
      const deps = packageJson.dependencies || {};
      const devDeps = packageJson.devDependencies || {};
      
      if (deps['@tensorflow/tfjs']) {
        console.log(`   ✅ TensorFlow.js dependency: ${deps['@tensorflow/tfjs']}`);
      } else if (deps['@tensorflow/tfjs-node']) {
        console.log(`   ✅ TensorFlow.js (node) dependency: ${deps['@tensorflow/tfjs-node']}`);
      } else {
        console.log(`   ❌ TensorFlow.js dependency missing`);
        return false;
      }
    }
    
    diagnosticResults.dependencies = true;
    return true;
  } catch (error) {
    console.log(`   ❌ Dependencies check error: ${error.message}`);
    return false;
  }
}

// 7. File System Permissions
async function checkPermissions() {
  console.log('\n7️⃣ CHECKING FILE PERMISSIONS...');
  try {
    const testDir = path.join(__dirname, '..', 'assets', 'models');
    
    // Check if we can create directory
    if (!fs.existsSync(testDir)) {
      fs.mkdirSync(testDir, { recursive: true });
      console.log(`   ✅ Created models directory: ${testDir}`);
    } else {
      console.log(`   ✅ Models directory exists: ${testDir}`);
    }
    
    // Test write permissions
    const testFile = path.join(testDir, 'test_write.txt');
    fs.writeFileSync(testFile, 'test');
    fs.unlinkSync(testFile);
    console.log(`   ✅ Write permissions OK`);
    
    diagnosticResults.permissions = true;
    return true;
  } catch (error) {
    console.log(`   ❌ Permissions error: ${error.message}`);
    return false;
  }
}

// 8. Memory and Backend
async function checkMemory() {
  console.log('\n8️⃣ CHECKING MEMORY AND BACKEND...');
  try {
    const tf = require('@tensorflow/tfjs');
    
    console.log(`   ✅ Backend: ${tf.getBackend()}`);
    console.log(`   ✅ Memory info: ${JSON.stringify(tf.memory())}`);
    
    // Test large tensor allocation
    const largeTensor = tf.randomNormal([100, 224, 224, 3]);
    console.log(`   ✅ Large tensor allocation: ${largeTensor.shape}`);
    largeTensor.dispose();
    
    diagnosticResults.memory = true;
    return true;
  } catch (error) {
    console.log(`   ❌ Memory/backend error: ${error.message}`);
    return false;
  }
}

// 9. Required Paths
async function checkPaths() {
  console.log('\n9️⃣ CHECKING REQUIRED PATHS...');
  try {
    const requiredPaths = [
      path.join(__dirname, '..', 'datasets'),
      path.join(__dirname, '..', 'assets'),
      path.join(__dirname, '..', 'scripts')
    ];
    
    for (const reqPath of requiredPaths) {
      if (fs.existsSync(reqPath)) {
        console.log(`   ✅ Path exists: ${reqPath}`);
      } else {
        console.log(`   ❌ Missing path: ${reqPath}`);
        return false;
      }
    }
    
    diagnosticResults.paths = true;
    return true;
  } catch (error) {
    console.log(`   ❌ Paths check error: ${error.message}`);
    return false;
  }
}

// 10. Minimal Training Test
async function checkMinimalTest() {
  console.log('\n🔟 MINIMAL TRAINING TEST...');
  try {
    const tf = require('@tensorflow/tfjs');
    
    // Create minimal model
    const model = tf.sequential({
      layers: [
        tf.layers.dense({ inputShape: [10], units: 5, activation: 'relu' }),
        tf.layers.dense({ units: 1 })
      ]
    });
    
    model.compile({
      optimizer: 'adam',
      loss: 'meanSquaredError'
    });
    
    // Create minimal data
    const xs = tf.randomNormal([20, 10]);
    const ys = tf.randomNormal([20, 1]);
    
    console.log(`   ✅ Minimal model and data created`);
    
    // Test single epoch training
    const history = await model.fit(xs, ys, {
      epochs: 1,
      verbose: 0
    });
    
    console.log(`   ✅ Training completed! Loss: ${history.history.loss[0].toFixed(4)}`);
    
    // Cleanup
    model.dispose();
    xs.dispose();
    ys.dispose();
    
    diagnosticResults.minimalTest = true;
    return true;
  } catch (error) {
    console.log(`   ❌ Minimal test error: ${error.message}`);
    console.log(`   📋 Stack trace: ${error.stack}`);
    return false;
  }
}

// Main diagnostic function
async function runDiagnostics() {
  console.log('Starting comprehensive diagnostics...\n');
  
  const checks = [
    { name: 'TensorFlow.js', fn: checkTensorFlow },
    { name: 'Module Imports', fn: checkImports },
    { name: 'Dataset Structure', fn: checkDatasets },
    { name: 'Model Creation', fn: checkModelCreation },
    { name: 'Tensor Operations', fn: checkTensorOps },
    { name: 'Dependencies', fn: checkDependencies },
    { name: 'File Permissions', fn: checkPermissions },
    { name: 'Memory/Backend', fn: checkMemory },
    { name: 'Required Paths', fn: checkPaths },
    { name: 'Minimal Training', fn: checkMinimalTest }
  ];
  
  for (const check of checks) {
    await check.fn();
  }
  
  // Summary
  console.log('\n' + '=' .repeat(50));
  console.log('📊 DIAGNOSTIC SUMMARY');
  console.log('=' .repeat(50));
  
  const passed = Object.values(diagnosticResults).filter(Boolean).length;
  const total = Object.keys(diagnosticResults).length;
  
  console.log(`Overall Status: ${passed}/${total} checks passed`);
  
  for (const [key, value] of Object.entries(diagnosticResults)) {
    const status = value ? '✅' : '❌';
    console.log(`${status} ${key}`);
  }
  
  if (passed === total) {
    console.log('\n🎉 ALL DIAGNOSTICS PASSED! Training should work.');
  } else {
    console.log('\n⚠️  Some diagnostics failed. Please fix the issues above.');
  }
}

// Run diagnostics
runDiagnostics().catch(console.error);
