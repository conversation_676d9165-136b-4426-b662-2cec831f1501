#!/usr/bin/env node

/**
 * Test Fixed TensorFlow.js React Native Installation
 * Verify all components work correctly
 */

console.log('🔍 Testing Fixed TensorFlow.js React Native Installation');
console.log('=' .repeat(55));

async function testInstallation() {
  try {
    console.log('📦 Step 1: Testing TensorFlow.js core...');
    const tf = require('@tensorflow/tfjs');
    console.log(`   ✅ TensorFlow.js version: ${tf.version.tfjs}`);
    
    console.log('📦 Step 2: Testing TensorFlow.js React Native...');
    // Import React Native components
    require('@tensorflow/tfjs-react-native');
    console.log(`   ✅ TensorFlow.js React Native loaded successfully`);
    
    console.log('📦 Step 3: Testing backends...');
    console.log(`   ✅ Backend: ${tf.getBackend()}`);
    console.log(`   ✅ Memory: ${JSON.stringify(tf.memory())}`);
    
    console.log('📦 Step 4: Testing tensor operations...');
    const testTensor = tf.randomNormal([10, 10]);
    const result = await testTensor.sum().data();
    console.log(`   ✅ Tensor operations work: sum = ${result[0].toFixed(3)}`);
    testTensor.dispose();
    
    console.log('📦 Step 5: Testing model creation...');
    const model = tf.sequential();
    model.add(tf.layers.dense({ inputShape: [10], units: 5, activation: 'relu' }));
    model.add(tf.layers.dense({ units: 1 }));
    model.compile({ optimizer: 'adam', loss: 'meanSquaredError' });
    console.log(`   ✅ Model created: ${model.countParams()} parameters`);
    
    console.log('📦 Step 6: Testing training...');
    const xs = tf.randomNormal([20, 10]);
    const ys = tf.randomNormal([20, 1]);
    
    const history = await model.fit(xs, ys, {
      epochs: 3,
      verbose: 0
    });
    
    console.log(`   ✅ Training works: final loss = ${history.history.loss[2].toFixed(4)}`);
    
    // Cleanup
    model.dispose();
    xs.dispose();
    ys.dispose();
    
    console.log('\n🎉 ALL TESTS PASSED!');
    console.log('✅ TensorFlow.js React Native is properly installed');
    console.log('✅ Ready for CNN training');
    console.log('✅ Compatible with Expo deployment');
    
    return true;
    
  } catch (error) {
    console.log(`\n❌ TEST FAILED: ${error.message}`);
    console.log(`Stack: ${error.stack}`);
    return false;
  }
}

// Run the test
testInstallation().then(success => {
  if (success) {
    console.log('\n🚀 Ready to proceed with CNN training!');
  } else {
    console.log('\n⚠️  Installation needs further fixes');
  }
});
