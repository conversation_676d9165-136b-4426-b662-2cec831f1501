# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: tensorflow/tsl/protobuf/distributed_runtime_payloads.proto
"""Generated protocol buffer code."""
from google.protobuf.internal import builder as _builder
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n:tensorflow/tsl/protobuf/distributed_runtime_payloads.proto\x12\x1etensorflow.distributed_runtime\"\x9d\x01\n\x14GrpcPayloadContainer\x12T\n\x08payloads\x18\x01 \x03(\x0b\x32\x42.tensorflow.distributed_runtime.GrpcPayloadContainer.PayloadsEntry\x1a/\n\rPayloadsEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\x0c:\x02\x38\x01\"\x12\n\x10GrpcPayloadsLost\"\x19\n\x17WorkerPossiblyRestartedBAZ<github.com/tsl/tsl/go/core/protobuf/for_core_protos_go_proto\xf8\x01\x01\x62\x06proto3')

_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, globals())
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'tensorflow.tsl.protobuf.distributed_runtime_payloads_pb2', globals())
if _descriptor._USE_C_DESCRIPTORS == False:

  DESCRIPTOR._options = None
  DESCRIPTOR._serialized_options = b'Z<github.com/tsl/tsl/go/core/protobuf/for_core_protos_go_proto\370\001\001'
  _GRPCPAYLOADCONTAINER_PAYLOADSENTRY._options = None
  _GRPCPAYLOADCONTAINER_PAYLOADSENTRY._serialized_options = b'8\001'
  _GRPCPAYLOADCONTAINER._serialized_start=95
  _GRPCPAYLOADCONTAINER._serialized_end=252
  _GRPCPAYLOADCONTAINER_PAYLOADSENTRY._serialized_start=205
  _GRPCPAYLOADCONTAINER_PAYLOADSENTRY._serialized_end=252
  _GRPCPAYLOADSLOST._serialized_start=254
  _GRPCPAYLOADSLOST._serialized_end=272
  _WORKERPOSSIBLYRESTARTED._serialized_start=274
  _WORKERPOSSIBLYRESTARTED._serialized_end=299
# @@protoc_insertion_point(module_scope)
