import { DarkTheme, DefaultTheme, ThemeProvider as NavigationThemeProvider } from '@react-navigation/native';
import { useFonts } from 'expo-font';
import { Stack } from 'expo-router';
import { StatusBar } from 'expo-status-bar';
import { useEffect } from 'react';
import 'react-native-reanimated';

import { ErrorBoundary } from '@/components/ErrorBoundary';
import { AuthProvider } from '@/contexts/AuthContext';
import { ThemeProvider, useTheme } from '@/contexts/ThemeContext';
import { hideSplashScreen, initializeSplashScreen } from '@/lib/splashScreen';

function AppContent() {
  const { colorScheme, colors } = useTheme();

  // Initialize environment validation and TensorFlow.js
  useEffect(() => {
    const initializeApp = async () => {
      try {
        const { env } = await import('@/utils/env');
        const { log } = await import('@/utils/logger');

        // Validate environment configuration
        const config = env.validateAndGetConfig();
        log.info('App initialized successfully', 'App', {
          environment: config.isDevelopment ? 'development' : 'production',
          version: config.appVersion,
        });

        // Initialize FootFit AI Analysis Service in background (non-blocking)
        log.info('Starting FootFit AI Analysis Service initialization...', 'App');

        // Initialize AI service asynchronously without blocking app startup
        const initializeAIService = async () => {
          let initializationAttempts = 0;
          const maxAttempts = 3;

          const attemptInitialization = async (): Promise<boolean> => {
            try {
              initializationAttempts++;
              log.info(`Attempting AI service initialization (${initializationAttempts}/${maxAttempts})`, 'App');

              const { FootAnalysisAI } = await import('@/services/footAnalysisAI');

              // Add timeout to initialization
              const initTimeout = new Promise<boolean>((_, reject) => {
                setTimeout(() => reject(new Error('AI initialization timeout')), 30000);
              });

              const aiInitialized = await Promise.race([
                FootAnalysisAI.initialize(),
                initTimeout
              ]);

              if (aiInitialized) {
                log.info('FootFit AI Analysis Service initialized successfully', 'App');

                // Test the service in background with timeout
                try {
                  const testTimeout = new Promise<boolean>((_, reject) => {
                    setTimeout(() => reject(new Error('AI service test timeout')), 10000);
                  });

                  const testPassed = await Promise.race([
                    FootAnalysisAI.testService(),
                    testTimeout
                  ]);

                  if (testPassed) {
                    log.info('FootFit AI Analysis Service test passed', 'App');
                  } else {
                    log.warn('FootFit AI Analysis Service test failed, but service is available', 'App');
                  }
                } catch (testError) {
                  log.warn('FootFit AI Analysis Service test failed with error', 'App', testError);
                }

                return true;
              } else {
                log.warn(`FootFit AI Analysis Service initialization failed (attempt ${initializationAttempts})`, 'App');
                return false;
              }
            } catch (error) {
              log.error(`Error initializing FootFit AI Analysis Service (attempt ${initializationAttempts})`, 'App', error);
              return false;
            }
          };

          // Try initialization with retries
          for (let attempt = 0; attempt < maxAttempts; attempt++) {
            const success = await attemptInitialization();
            if (success) {
              return;
            }

            // Wait before retry (progressive backoff)
            if (attempt < maxAttempts - 1) {
              const delay = Math.min(2000 * Math.pow(2, attempt), 8000);
              log.info(`Retrying AI service initialization in ${delay}ms`, 'App');
              await new Promise(resolve => setTimeout(resolve, delay));
            }
          }

          log.error('FootFit AI Analysis Service initialization failed after all attempts', 'App', {
            attempts: maxAttempts,
            fallbackMode: 'App will continue with limited functionality'
          });
        };

        // Start AI initialization in background
        initializeAIService();

      } catch (error) {
        const { log } = await import('@/utils/logger');
        log.error('App initialization failed', 'App', error);
        // In production, this should show a user-friendly error screen
        if (process.env.NODE_ENV === 'production') {
          // Could show an error boundary or maintenance screen
        }
      }
    };

    initializeApp();
  }, []);

  // Handle authentication redirects
  const { useAuthRedirect } = require('@/hooks/useAuthRedirect');
  useAuthRedirect();



  // Create custom navigation theme based on our colors
  const navigationTheme = {
    ...(colorScheme === 'dark' ? DarkTheme : DefaultTheme),
    colors: {
      ...(colorScheme === 'dark' ? DarkTheme.colors : DefaultTheme.colors),
      primary: colors.primary,
      background: colors.background,
      card: colors.background,
      text: colors.text,
      border: colors.border,
      notification: colors.primary,
    },
  };

  return (
    <NavigationThemeProvider value={navigationTheme}>
      <Stack>
        <Stack.Screen name="(tabs)" options={{ headerShown: false }} />
        <Stack.Screen name="upload" options={{ headerShown: false }} />
        <Stack.Screen name="camera" options={{ headerShown: false }} />
        <Stack.Screen name="processing" options={{ headerShown: false }} />
        <Stack.Screen name="results" options={{ headerShown: false }} />
        <Stack.Screen name="profile/edit" options={{ headerShown: false }} />
        <Stack.Screen name="auth/login" options={{ headerShown: false }} />
        <Stack.Screen name="auth/signup" options={{ headerShown: false }} />
        <Stack.Screen name="auth/forgot-password" options={{ headerShown: false }} />
        <Stack.Screen name="+not-found" />
      </Stack>
      <StatusBar style={colorScheme === 'dark' ? 'light' : 'dark'} />


    </NavigationThemeProvider>
  );
}

export default function RootLayout() {
  const [loaded] = useFonts({
    SpaceMono: require('../assets/fonts/SpaceMono-Regular.ttf'),
  });

  // Initialize splash screen and services
  useEffect(() => {
    const initializeApp = async () => {
      // Initialize splash screen first
      await initializeSplashScreen();

      // App initialization complete
    };

    initializeApp();
  }, []);

  // Hide splash screen when fonts are loaded
  useEffect(() => {
    if (loaded) {
      hideSplashScreen();
    }
  }, [loaded]);

  if (!loaded) {
    // Async font loading only occurs in development.
    return null;
  }

  return (
    <ThemeProvider>
      <ErrorBoundary>
        <AuthProvider>
          <AppContent />
        </AuthProvider>
      </ErrorBoundary>
    </ThemeProvider>
  );
}
