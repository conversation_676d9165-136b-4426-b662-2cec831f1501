#!/usr/bin/env node

/**
 * TensorFlow.js Node Functionality Test
 * Verifies that @tensorflow/tfjs-node is properly installed and functional
 */

console.log('🧪 Testing TensorFlow.js Node Installation...');
console.log('');

try {
  // Test 1: Import TensorFlow.js Node
  console.log('📦 Test 1: Loading TensorFlow.js Node...');
  const tf = require('@tensorflow/tfjs-node');
  console.log('✅ TensorFlow.js Node loaded successfully');
  console.log(`   Version: ${tf.version.tfjs}`);
  console.log(`   Backend: ${tf.getBackend()}`);
  console.log('');

  // Test 2: Basic tensor operations
  console.log('🔢 Test 2: Basic tensor operations...');
  const a = tf.tensor2d([[1, 2], [3, 4]]);
  const b = tf.tensor2d([[5, 6], [7, 8]]);
  const c = a.add(b);
  
  console.log('   Created tensors a and b');
  console.log('   Computed c = a + b');
  console.log(`   Result shape: [${c.shape}]`);
  console.log(`   Result values: [${c.dataSync()}]`);
  
  // Clean up tensors
  a.dispose();
  b.dispose();
  c.dispose();
  console.log('✅ Basic tensor operations working');
  console.log('');

  // Test 3: Image processing capabilities
  console.log('🖼️  Test 3: Image processing capabilities...');
  const imageShape = [1, 224, 224, 3]; // Batch, height, width, channels
  const dummyImage = tf.randomNormal(imageShape);
  
  // Test resize operation (needed for training)
  const resized = tf.image.resizeBilinear(dummyImage, [224, 224]);
  console.log(`   Created dummy image tensor: [${dummyImage.shape}]`);
  console.log(`   Resized image tensor: [${resized.shape}]`);
  
  // Test normalization (needed for training)
  const normalized = resized.div(255.0);
  console.log(`   Normalized image tensor: [${normalized.shape}]`);
  
  // Clean up
  dummyImage.dispose();
  resized.dispose();
  normalized.dispose();
  console.log('✅ Image processing capabilities working');
  console.log('');

  // Test 4: CNN model creation
  console.log('🏗️  Test 4: CNN model creation...');
  const model = tf.sequential({
    layers: [
      tf.layers.conv2d({
        inputShape: [224, 224, 3],
        filters: 32,
        kernelSize: 3,
        activation: 'relu',
        padding: 'same',
      }),
      tf.layers.maxPooling2d({ poolSize: 2 }),
      tf.layers.conv2d({
        filters: 64,
        kernelSize: 3,
        activation: 'relu',
        padding: 'same',
      }),
      tf.layers.globalAveragePooling2d({}),
      tf.layers.dense({ units: 4, activation: 'linear' }),
    ],
  });

  console.log(`   Created CNN model with ${model.layers.length} layers`);
  console.log(`   Total parameters: ${model.countParams()}`);
  console.log('✅ CNN model creation working');
  console.log('');

  // Test 5: Model compilation
  console.log('⚙️  Test 5: Model compilation...');
  model.compile({
    optimizer: tf.train.adam(0.001),
    loss: 'meanSquaredError',
    metrics: ['mae']
  });
  console.log('✅ Model compilation working');
  console.log('');

  // Test 6: Model prediction
  console.log('🔮 Test 6: Model prediction...');
  const testInput = tf.randomNormal([1, 224, 224, 3]);
  const prediction = model.predict(testInput);
  console.log(`   Input shape: [${testInput.shape}]`);
  console.log(`   Prediction shape: [${prediction.shape}]`);
  console.log(`   Prediction values: [${prediction.dataSync()}]`);
  
  // Clean up
  testInput.dispose();
  prediction.dispose();
  model.dispose();
  console.log('✅ Model prediction working');
  console.log('');

  // Test 7: File system operations (needed for dataset loading)
  console.log('📁 Test 7: File system operations...');
  const fs = require('fs');
  const path = require('path');
  
  // Check if datasets directory exists
  const datasetPath = path.join(__dirname, '..', 'datasets');
  if (fs.existsSync(datasetPath)) {
    console.log(`   ✅ Dataset directory found: ${datasetPath}`);
    
    // Check for training images
    const trainImagesPath = path.join(datasetPath, 'train', 'images');
    if (fs.existsSync(trainImagesPath)) {
      const imageFiles = fs.readdirSync(trainImagesPath)
        .filter(f => f.endsWith('.jpg') || f.endsWith('.png'));
      console.log(`   ✅ Found ${imageFiles.length} training images`);
    } else {
      console.log('   ⚠️  Training images directory not found');
    }
  } else {
    console.log('   ⚠️  Dataset directory not found');
  }
  console.log('');

  // Test 8: Memory management
  console.log('🧠 Test 8: Memory management...');
  const memoryBefore = tf.memory();
  console.log(`   Memory before: ${memoryBefore.numTensors} tensors, ${memoryBefore.numBytes} bytes`);
  
  // Create and dispose some tensors
  const tensors = [];
  for (let i = 0; i < 10; i++) {
    tensors.push(tf.randomNormal([100, 100]));
  }
  
  const memoryDuring = tf.memory();
  console.log(`   Memory during: ${memoryDuring.numTensors} tensors, ${memoryDuring.numBytes} bytes`);
  
  // Dispose all tensors
  tensors.forEach(t => t.dispose());
  
  const memoryAfter = tf.memory();
  console.log(`   Memory after: ${memoryAfter.numTensors} tensors, ${memoryAfter.numBytes} bytes`);
  console.log('✅ Memory management working');
  console.log('');

  // Final summary
  console.log('🎉 ALL TESTS PASSED!');
  console.log('');
  console.log('✅ TensorFlow.js Node is properly installed and functional');
  console.log('✅ Ready for real CNN model training');
  console.log('✅ Image processing capabilities confirmed');
  console.log('✅ Model creation and compilation working');
  console.log('✅ File system access for dataset loading confirmed');
  console.log('✅ Memory management working properly');
  console.log('');
  console.log('🚀 You can now proceed with real model training!');

} catch (error) {
  console.error('❌ TensorFlow.js Node test failed:');
  console.error('Error:', error.message);
  console.error('Stack:', error.stack);
  process.exit(1);
}
