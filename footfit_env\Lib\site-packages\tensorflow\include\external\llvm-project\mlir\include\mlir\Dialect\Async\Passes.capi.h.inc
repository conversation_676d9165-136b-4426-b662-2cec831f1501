
/* Autogenerated by mlir-tblgen; don't manually edit. */

#include "mlir-c/Pass.h"

#ifdef __cplusplus
extern "C" {
#endif

// Registration for the entire group
MLIR_CAPI_EXPORTED void mlirRegisterAsyncPasses(void);


/* Create Async Pass. */
MLIR_CAPI_EXPORTED MlirPass mlirCreateAsyncAsyncFuncToAsyncRuntime(void);
MLIR_CAPI_EXPORTED void mlirRegisterAsyncAsyncFuncToAsyncRuntime(void);


/* Create Async Pass. */
MLIR_CAPI_EXPORTED MlirPass mlirCreateAsyncAsyncParallelFor(void);
MLIR_CAPI_EXPORTED void mlirRegisterAsyncAsyncParallelFor(void);


/* Create Async Pass. */
MLIR_CAPI_EXPORTED MlirPass mlirCreateAsyncAsyncRuntimePolicyBasedRefCounting(void);
MLIR_CAPI_EXPORTED void mlirRegisterAsyncAsyncRuntimePolicyBasedRefCounting(void);


/* Create Async Pass. */
MLIR_CAPI_EXPORTED MlirPass mlirCreateAsyncAsyncRuntimeRefCounting(void);
MLIR_CAPI_EXPORTED void mlirRegisterAsyncAsyncRuntimeRefCounting(void);


/* Create Async Pass. */
MLIR_CAPI_EXPORTED MlirPass mlirCreateAsyncAsyncRuntimeRefCountingOpt(void);
MLIR_CAPI_EXPORTED void mlirRegisterAsyncAsyncRuntimeRefCountingOpt(void);


/* Create Async Pass. */
MLIR_CAPI_EXPORTED MlirPass mlirCreateAsyncAsyncToAsyncRuntime(void);
MLIR_CAPI_EXPORTED void mlirRegisterAsyncAsyncToAsyncRuntime(void);



#ifdef __cplusplus
}
#endif
