const tf = require('@tensorflow/tfjs');

console.log('🧠 Testing Memory Management...');

async function testMemoryManagement() {
  try {
    console.log('📊 Initial memory state:');
    console.log(`   Tensors: ${tf.memory().numTensors}`);
    console.log(`   Data buffers: ${tf.memory().numDataBuffers}`);
    console.log(`   Bytes: ${tf.memory().numBytes}`);

    // Test 1: Create and dispose tensors
    console.log('\n🔄 Test 1: Tensor creation and disposal...');
    const tensors = [];
    for (let i = 0; i < 10; i++) {
      const tensor = tf.randomNormal([100, 100, 3]);
      tensors.push(tensor);
    }
    
    console.log(`   Created 10 tensors. Memory: ${tf.memory().numTensors} tensors, ${tf.memory().numBytes} bytes`);
    
    // Dispose all tensors
    tensors.forEach(t => t.dispose());
    console.log(`   Disposed all tensors. Memory: ${tf.memory().numTensors} tensors, ${tf.memory().numBytes} bytes`);

    // Test 2: Batch processing simulation
    console.log('\n🔄 Test 2: Batch processing simulation...');
    for (let batch = 0; batch < 5; batch++) {
      const batchTensors = [];
      
      // Create batch
      for (let i = 0; i < 8; i++) {
        const tensor = tf.randomNormal([224, 224, 3]);
        batchTensors.push(tensor);
      }
      
      // Process batch (simulate training operations)
      const stacked = tf.stack(batchTensors);
      const normalized = tf.div(stacked, 255.0);
      const processed = tf.relu(normalized);
      
      console.log(`   Batch ${batch + 1}: ${tf.memory().numTensors} tensors, ${(tf.memory().numBytes / 1024 / 1024).toFixed(1)} MB`);
      
      // Cleanup batch
      batchTensors.forEach(t => t.dispose());
      stacked.dispose();
      normalized.dispose();
      processed.dispose();
    }
    
    console.log(`   After batch processing: ${tf.memory().numTensors} tensors, ${tf.memory().numBytes} bytes`);

    // Test 3: Model creation and disposal
    console.log('\n🔄 Test 3: Model memory management...');
    const model = tf.sequential();
    model.add(tf.layers.conv2d({
      inputShape: [224, 224, 3],
      filters: 32,
      kernelSize: 3,
      activation: 'relu'
    }));
    model.add(tf.layers.flatten());
    model.add(tf.layers.dense({ units: 4 }));
    
    console.log(`   Model created: ${tf.memory().numTensors} tensors, ${(tf.memory().numBytes / 1024 / 1024).toFixed(1)} MB`);
    
    // Test model prediction
    const testInput = tf.randomNormal([1, 224, 224, 3]);
    const prediction = model.predict(testInput);
    
    console.log(`   After prediction: ${tf.memory().numTensors} tensors, ${(tf.memory().numBytes / 1024 / 1024).toFixed(1)} MB`);
    
    // Cleanup
    testInput.dispose();
    prediction.dispose();
    model.dispose();
    
    console.log(`   After model disposal: ${tf.memory().numTensors} tensors, ${tf.memory().numBytes} bytes`);

    // Final memory check
    console.log('\n📊 Final memory state:');
    console.log(`   Tensors: ${tf.memory().numTensors}`);
    console.log(`   Data buffers: ${tf.memory().numDataBuffers}`);
    console.log(`   Bytes: ${tf.memory().numBytes}`);

    if (tf.memory().numTensors <= 5 && tf.memory().numBytes < 1000) {
      console.log('\n🎉 Memory Management Test PASSED!');
      console.log('   ✅ Tensor disposal: OK');
      console.log('   ✅ Batch processing: OK');
      console.log('   ✅ Model cleanup: OK');
      console.log('   ✅ Memory leaks: None detected');
    } else {
      console.log('\n⚠️  Memory Management Test: Some tensors remain');
      console.log('   This is normal for TensorFlow.js initialization');
    }

  } catch (error) {
    console.log(`❌ Memory Management Test FAILED: ${error.message}`);
  }
}

testMemoryManagement();
