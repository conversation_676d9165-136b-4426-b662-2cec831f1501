const tf = require('@tensorflow/tfjs');

console.log('Testing TensorFlow.js model creation...');

try {
  console.log('Creating sequential model...');
  const model = tf.sequential();
  
  console.log('Adding input layer...');
  model.add(tf.layers.dense({
    inputShape: [10],
    units: 5,
    activation: 'relu'
  }));
  
  console.log('Adding output layer...');
  model.add(tf.layers.dense({
    units: 1
  }));
  
  console.log('✅ Simple model created successfully!');
  
  console.log('Testing conv2d layer...');
  const convModel = tf.sequential();
  
  convModel.add(tf.layers.conv2d({
    inputShape: [28, 28, 1],
    filters: 8,
    kernelSize: 3,
    activation: 'relu'
  }));
  
  console.log('✅ Conv2D model created successfully!');
  
  console.log('Testing complex model...');
  const complexModel = tf.sequential();
  
  complexModel.add(tf.layers.conv2d({
    inputShape: [224, 224, 3],
    filters: 32,
    kernelSize: 3,
    activation: 'relu'
  }));
  
  complexModel.add(tf.layers.maxPooling2d({ 
    poolSize: [2, 2] 
  }));
  
  complexModel.add(tf.layers.flatten());
  
  complexModel.add(tf.layers.dense({ 
    units: 4 
  }));
  
  console.log('✅ Complex model created successfully!');
  console.log(`Model parameters: ${complexModel.countParams()}`);
  
} catch (error) {
  console.log('❌ Error:', error.message);
  console.log('Stack:', error.stack);
}
