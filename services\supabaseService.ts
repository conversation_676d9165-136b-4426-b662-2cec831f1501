import type { Database } from '@/lib/supabase';
import { supabase } from '@/lib/supabase';
import { log } from '@/utils/logger';
import AsyncStorage from '@react-native-async-storage/async-storage';
import type { ShoeRecommendation as MockAIShoeRecommendation } from './types';

// Type definitions
type Profile = Database['public']['Tables']['profiles']['Row'];
type ProfileInsert = Database['public']['Tables']['profiles']['Insert'];
type ProfileUpdate = Database['public']['Tables']['profiles']['Update'];

type Measurement = Database['public']['Tables']['measurements']['Row'];
type MeasurementInsert = Database['public']['Tables']['measurements']['Insert'];

type ShoeRecommendation = Database['public']['Tables']['shoe_recommendations']['Row'];
type ShoeRecommendationInsert = Database['public']['Tables']['shoe_recommendations']['Insert'];

// Shoe database types
interface SupabaseBrand {
  id: string;
  name: string;
  logo_url: string;
  description: string;
  created_at: string;
  updated_at: string;
}

interface SupabaseCategory {
  id: string;
  name: string;
  description: string;
  created_at: string;
  updated_at: string;
}

interface SupabaseShoeModel {
  id: string;
  brand_id: string;
  name: string;
  description: string;
  image_url: string;
  price: number;
  size_range: {
    uk_min: number;
    uk_max: number;
    us_min: number;
    us_max: number;
    eu_min: number;
    eu_max: number;
  };
  created_at: string;
  updated_at: string;
}

interface BrandCategoryMapping {
  brand_id: string;
  category_id: string;
}

// Import ShoeRecommendation type from mockAI for compatibility

// Guest mode storage keys
const GUEST_STORAGE_KEYS = {
  MEASUREMENTS: 'guest_measurements',
  PREFERENCES: 'guest_preferences',
} as const;

export interface MeasurementWithRecommendations extends Measurement {
  recommendations: ShoeRecommendation[];
}

export interface UploadImageResult {
  success: boolean;
  url?: string;
  error?: string;
}

export interface SaveMeasurementResult {
  success: boolean;
  measurementId?: string;
  error?: string;
}

// Enhanced error handling utilities
interface NetworkError {
  isNetworkError: boolean;
  isOffline: boolean;
  message: string;
  originalError: any;
}

class SupabaseErrorHandler {
  static async handleError(error: any, operation: string): Promise<NetworkError> {
    const { log } = await import('@/utils/logger');

    const networkError: NetworkError = {
      isNetworkError: false,
      isOffline: false,
      message: 'An unexpected error occurred',
      originalError: error,
    };

    if (!error) {
      return networkError;
    }

    // Check for network-related errors
    if (error.message?.includes('Network request failed') ||
        error.message?.includes('fetch') ||
        error.code === 'NETWORK_ERROR') {
      networkError.isNetworkError = true;
      networkError.isOffline = true;
      networkError.message = 'No internet connection. Please check your network and try again.';
      log.warn(`Network error in ${operation}`, 'SupabaseService', error);
    }
    // Check for timeout errors
    else if (error.message?.includes('timeout') || error.code === 'TIMEOUT') {
      networkError.isNetworkError = true;
      networkError.message = 'Request timed out. Please try again.';
      log.warn(`Timeout error in ${operation}`, 'SupabaseService', error);
    }
    // Check for authentication errors
    else if (error.message?.includes('JWT') || error.message?.includes('auth') || error.status === 401) {
      networkError.message = 'Authentication error. Please sign in again.';
      log.error(`Auth error in ${operation}`, 'SupabaseService', error);
    }
    // Check for permission errors
    else if (error.message?.includes('permission') || error.status === 403) {
      networkError.message = 'Permission denied. Please contact support.';
      log.error(`Permission error in ${operation}`, 'SupabaseService', error);
    }
    // Check for server errors
    else if (error.status >= 500) {
      networkError.message = 'Server error. Please try again later.';
      log.error(`Server error in ${operation}`, 'SupabaseService', error);
    }
    // Generic error
    else {
      networkError.message = error.message || 'An unexpected error occurred';
      log.error(`Error in ${operation}`, 'SupabaseService', error);
    }

    return networkError;
  }

  static async withRetry<T>(
    operation: () => Promise<T>,
    operationName: string,
    maxRetries: number = 3,
    delay: number = 1000
  ): Promise<T> {
    const { log } = await import('@/utils/logger');

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        return await operation();
      } catch (error) {
        const networkError = await this.handleError(error, operationName);

        if (attempt === maxRetries || !networkError.isNetworkError) {
          throw error;
        }

        log.debug(`Retrying ${operationName} (attempt ${attempt}/${maxRetries})`, 'SupabaseService');
        await new Promise(resolve => setTimeout(resolve, delay * attempt));
      }
    }

    throw new Error(`Failed after ${maxRetries} attempts`);
  }
}

export class SupabaseService {
  // Connection testing
  static async testConnection(): Promise<{ connected: boolean; error?: string }> {
    try {
      const { log } = await import('@/utils/logger');
      log.debug('Testing Supabase connection', 'SupabaseService');

      // Simple connection test with timeout
      const testPromise = supabase.from('profiles').select('id').limit(1);
      const timeoutPromise = new Promise((_, reject) => {
        setTimeout(() => reject(new Error('Connection test timeout')), 5000);
      });

      await Promise.race([testPromise, timeoutPromise]);

      log.debug('Supabase connection test successful', 'SupabaseService');
      return { connected: true };
    } catch (error) {
      const { log } = await import('@/utils/logger');
      log.warn('Supabase connection test failed', 'SupabaseService', error);

      const errorMessage = error instanceof Error ? error.message : 'Connection test failed';
      return {
        connected: false,
        error: errorMessage.includes('timeout') ? 'Connection timeout' : 'No internet connection'
      };
    }
  }

  // Profile operations
  static async getProfile(userId: string): Promise<{ data: Profile | null; error: string | null }> {
    return SupabaseErrorHandler.withRetry(async () => {
      const { data, error } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', userId)
        .single();

      if (error) {
        throw error;
      }

      return { data, error: null };
    }, 'getProfile').catch(async (error) => {
      const networkError = await SupabaseErrorHandler.handleError(error, 'getProfile');
      return { data: null, error: networkError.message };
    });
  }

  static async updateProfile(userId: string, updates: ProfileUpdate): Promise<{ error: string | null }> {
    return SupabaseErrorHandler.withRetry(async () => {
      const { error } = await supabase
        .from('profiles')
        .update({
          ...updates,
          updated_at: new Date().toISOString(),
        })
        .eq('id', userId);

      if (error) {
        throw error;
      }

      return { error: null };
    }, 'updateProfile').catch(async (error) => {
      const networkError = await SupabaseErrorHandler.handleError(error, 'updateProfile');
      return { error: networkError.message };
    });
  }

  // Enhanced image upload operations with processing, compression, and retry
  static async uploadFootImage(userId: string, imageUri: string): Promise<UploadImageResult> {
    const maxRetries = 3;
    const timeoutMs = 45000; // 45 seconds (increased for processing time)

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        console.log(`Upload attempt ${attempt} for user ${userId}`);

        // Step 1: Process image (compress, resize to square, optimize)
        const ImageManipulator = await import('expo-image-manipulator');
        const processResult = await ImageManipulator.manipulateAsync(
          imageUri,
          [{ resize: { width: 1024, height: 1024 } }],
          {
            compress: 0.8,
            format: ImageManipulator.SaveFormat.JPEG,
            base64: false,
          }
        );

        console.log(`Processed image: 1024x1024`);

        // Step 2: Create blob from processed image
        const response = await fetch(processResult.uri);
        const blob = await response.blob();

        // Validate blob
        if (blob.size === 0) {
          throw new Error('Processed image is empty');
        }

        if (blob.size > 5 * 1024 * 1024) { // 5MB limit (reduced from 10MB)
          throw new Error('Processed image is still too large');
        }

        console.log(`Final blob size: ${(blob.size / 1024).toFixed(1)}KB`);

        // Step 3: Upload to Supabase Storage

        // Generate unique filename with square indicator
        const timestamp = Date.now();
        const filename = `${userId}/${timestamp}-foot-square.jpg`;

        // Add timeout to upload operation
        const uploadPromise = supabase.storage
          .from('foot-images')
          .upload(filename, blob, {
            contentType: 'image/jpeg',
            upsert: false,
            cacheControl: '3600', // Cache for 1 hour
          });

        const uploadTimeoutPromise = new Promise((_, reject) => {
          setTimeout(() => reject(new Error('Upload timeout')), timeoutMs);
        });

        const { data, error } = await Promise.race([uploadPromise, uploadTimeoutPromise]) as any;

        if (error) {
          console.error(`Upload error (attempt ${attempt}):`, error);

          // Don't retry on certain errors
          if (error.message.includes('already exists') || error.message.includes('permission')) {
            return { success: false, error: error.message };
          }

          // Retry on network/timeout errors
          if (attempt < maxRetries) {
            console.log(`Retrying upload in ${attempt * 1000}ms...`);
            await new Promise(resolve => setTimeout(resolve, attempt * 1000));
            continue;
          }

          return { success: false, error: error.message };
        }

        // Get public URL
        const { data: urlData } = supabase.storage
          .from('foot-images')
          .getPublicUrl(data.path);

        // Validate URL
        if (!urlData.publicUrl) {
          throw new Error('Failed to generate public URL');
        }

        return {
          success: true,
          url: urlData.publicUrl,
        };

      } catch (error) {
        console.error(`Upload service error (attempt ${attempt}):`, error);

        const errorMessage = error instanceof Error ? error.message : 'Upload failed';

        // Don't retry on certain errors
        if (errorMessage.includes('permission') || errorMessage.includes('too large')) {
          return { success: false, error: errorMessage };
        }

        // Retry on network/timeout errors
        if (attempt < maxRetries) {
          console.log(`Retrying upload in ${attempt * 1000}ms...`);
          await new Promise(resolve => setTimeout(resolve, attempt * 1000));
          continue;
        }

        return {
          success: false,
          error: errorMessage,
        };
      }
    }

    return { success: false, error: 'Upload failed after all retries' };
  }

  static async deleteFootImage(imagePath: string): Promise<{ error: string | null }> {
    try {
      const { error } = await supabase.storage
        .from('foot-images')
        .remove([imagePath]);

      if (error) {
        return { error: error.message };
      }

      return { error: null };
    } catch (error) {
      return { error: 'Failed to delete image' };
    }
  }

  // Measurement operations
  static async saveMeasurement(
    userId: string,
    measurementData: {
      imageUrl: string;
      footLength: number;
      footWidth: number;
      recommendedSizeUk: string;
      recommendedSizeUs: string;
      recommendedSizeEu: string;
      confidence: number;
      recommendations: Array<{
        brand: string;
        model: string;
        sizeUk: string;
        sizeUs: string;
        sizeEu: string;
        confidence: number;
        fitType: 'narrow' | 'regular' | 'wide';
        category: string;
        imageUrl?: string;
      }>;
    }
  ): Promise<SaveMeasurementResult> {
    try {
      // Insert measurement
      const { data: measurement, error: measurementError } = await supabase
        .from('measurements')
        .insert({
          user_id: userId,
          image_url: measurementData.imageUrl,
          foot_length: measurementData.footLength,
          foot_width: measurementData.footWidth,
          recommended_size_uk: measurementData.recommendedSizeUk,
          recommended_size_us: measurementData.recommendedSizeUs,
          recommended_size_eu: measurementData.recommendedSizeEu,
          confidence: measurementData.confidence,
        })
        .select()
        .single();

      if (measurementError) {
        return { success: false, error: measurementError.message };
      }

      // Insert shoe recommendations
      if (measurementData.recommendations.length > 0) {
        const recommendationsToInsert: ShoeRecommendationInsert[] = measurementData.recommendations.map(rec => ({
          measurement_id: measurement.id,
          brand: rec.brand,
          model: rec.model,
          size_uk: rec.sizeUk,
          size_us: rec.sizeUs,
          size_eu: rec.sizeEu,
          confidence: rec.confidence,
          fit_type: rec.fitType,
          category: rec.category,
          image_url: rec.imageUrl || null,
        }));

        const { error: recommendationsError } = await supabase
          .from('shoe_recommendations')
          .insert(recommendationsToInsert);

        if (recommendationsError) {
          // If recommendations fail, we should still return success for the measurement
          console.error('Failed to save recommendations:', recommendationsError);
        }
      }

      return { success: true, measurementId: measurement.id };
    } catch (error) {
      return { success: false, error: 'Failed to save measurement' };
    }
  }

  static async getMeasurements(userId: string): Promise<{ data: MeasurementWithRecommendations[]; error: string | null }> {
    try {
      log.info('Fetching measurements for user', 'SupabaseService', { userId });

      // Test connection first
      const connectionTest = await this.testConnection();
      if (!connectionTest.connected) {
        return {
          data: [],
          error: connectionTest.error || 'Unable to connect to database. Please check your internet connection.'
        };
      }

      // Add timeout to the query
      const queryPromise = supabase
        .from('measurements')
        .select('*')
        .eq('user_id', userId)
        .order('created_at', { ascending: false });

      const timeoutPromise = new Promise((_, reject) => {
        setTimeout(() => reject(new Error('Query timeout')), 10000);
      });

      const { data, error } = await Promise.race([queryPromise, timeoutPromise]) as any;

      if (error) {
        log.error('Error fetching measurements from Supabase', 'SupabaseService', error);
        return { data: [], error: `Database error: ${error.message}` };
      }

      if (!data) {
        log.info('No measurements found for user', 'SupabaseService', { userId });
        return { data: [], error: null };
      }

      // Transform the data to match our interface (without recommendations for now)
      const measurements: MeasurementWithRecommendations[] = data.map((item: any) => ({
        ...item,
        recommendations: [], // Empty recommendations since we're not joining
      }));

      log.info(`Successfully fetched ${measurements.length} measurements`, 'SupabaseService', { userId });
      return { data: measurements, error: null };
    } catch (error) {
      log.error('Unexpected error fetching measurements', 'SupabaseService', error);

      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      if (errorMessage.includes('timeout')) {
        return { data: [], error: 'Request timed out. Please check your internet connection and try again.' };
      }

      return { data: [], error: 'Failed to fetch measurements. Please check your internet connection and try again.' };
    }
  }

  static async getMeasurement(measurementId: string): Promise<{ data: MeasurementWithRecommendations | null; error: string | null }> {
    try {
      const { data, error } = await supabase
        .from('measurements')
        .select(`
          *,
          recommendations:shoe_recommendations(*)
        `)
        .eq('id', measurementId)
        .single();

      if (error) {
        return { data: null, error: error.message };
      }

      const measurement: MeasurementWithRecommendations = {
        ...data,
        recommendations: data.recommendations || [],
      };

      return { data: measurement, error: null };
    } catch (error) {
      return { data: null, error: 'Failed to fetch measurement' };
    }
  }

  static async deleteMeasurement(measurementId: string): Promise<{ error: string | null }> {
    return SupabaseErrorHandler.withRetry(async () => {
      // Get measurement to find image URL and verify ownership
      const { data: measurement, error: fetchError } = await supabase
        .from('measurements')
        .select('image_url, user_id')
        .eq('id', measurementId)
        .single();

      if (fetchError) {
        throw new Error(`Failed to fetch measurement: ${fetchError.message}`);
      }

      if (!measurement) {
        throw new Error('Measurement not found');
      }

      // Delete measurement first (recommendations will be deleted by cascade)
      const { error: deleteError } = await supabase
        .from('measurements')
        .delete()
        .eq('id', measurementId);

      if (deleteError) {
        throw new Error(`Failed to delete measurement: ${deleteError.message}`);
      }

      // Delete associated image from storage
      if (measurement.image_url) {
        try {
          // Extract the full path from the URL
          // URL format: https://project.supabase.co/storage/v1/object/public/foot-images/userId/timestamp-foot-image.jpg
          const url = new URL(measurement.image_url);
          const pathParts = url.pathname.split('/');

          // Find the bucket name and construct the full path
          const bucketIndex = pathParts.findIndex(part => part === 'foot-images');
          if (bucketIndex !== -1 && bucketIndex < pathParts.length - 1) {
            // Get everything after the bucket name as the file path
            const imagePath = pathParts.slice(bucketIndex + 1).join('/');

            if (imagePath) {
              const { error: imageDeleteError } = await this.deleteFootImage(imagePath);
              if (imageDeleteError) {
                // Log the error but don't fail the entire operation
                console.warn('Failed to delete associated image:', imageDeleteError);
              }
            }
          }
        } catch (imageError) {
          // Log the error but don't fail the entire operation since the measurement is already deleted
          console.warn('Error processing image deletion:', imageError);
        }
      }

      return { error: null };
    }, 'deleteMeasurement').catch(async (error) => {
      const networkError = await SupabaseErrorHandler.handleError(error, 'deleteMeasurement');
      return { error: networkError.message };
    });
  }

  // Utility functions
  static async getUserStats(userId: string): Promise<{
    totalMeasurements: number;
    averageConfidence: number;
    mostRecentMeasurement: string | null;
  }> {
    try {
      log.info('Fetching user stats', 'SupabaseService', { userId });

      // Test connection first
      const connectionTest = await this.testConnection();
      if (!connectionTest.connected) {
        log.warn('Connection test failed for getUserStats', 'SupabaseService', { error: connectionTest.error });
        return {
          totalMeasurements: 0,
          averageConfidence: 0,
          mostRecentMeasurement: null,
        };
      }

      // Add timeout to the query
      const queryPromise = supabase
        .from('measurements')
        .select('confidence, created_at')
        .eq('user_id', userId);

      const timeoutPromise = new Promise((_, reject) => {
        setTimeout(() => reject(new Error('Stats query timeout')), 8000);
      });

      const { data, error } = await Promise.race([queryPromise, timeoutPromise]) as any;

      if (error) {
        log.error('Error fetching user stats from Supabase', 'SupabaseService', error);
        return {
          totalMeasurements: 0,
          averageConfidence: 0,
          mostRecentMeasurement: null,
        };
      }

      if (!data) {
        log.info('No measurement data found for user stats', 'SupabaseService', { userId });
        return {
          totalMeasurements: 0,
          averageConfidence: 0,
          mostRecentMeasurement: null,
        };
      }

      const totalMeasurements = data.length;
      const averageConfidence = totalMeasurements > 0
        ? data.reduce((sum: number, m: any) => sum + Number(m.confidence), 0) / totalMeasurements
        : 0;
      const mostRecentMeasurement = totalMeasurements > 0
        ? data.sort((a: any, b: any) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime())[0].created_at
        : null;

      log.info('Successfully calculated user stats', 'SupabaseService', {
        userId,
        totalMeasurements,
        averageConfidence,
      });

      return {
        totalMeasurements,
        averageConfidence,
        mostRecentMeasurement,
      };
    } catch (error) {
      log.error('Unexpected error fetching user stats', 'SupabaseService', error);

      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      if (errorMessage.includes('timeout')) {
        log.warn('User stats query timed out', 'SupabaseService', { userId });
      }

      return {
        totalMeasurements: 0,
        averageConfidence: 0,
        mostRecentMeasurement: null,
      };
    }
  }

  // Shoe recommendation methods (replacing CachedSupabaseService functionality)
  static async getRecommendations(params: {
    foot_length: number;
    foot_width: number;
    user_preferences?: any;
  }): Promise<MockAIShoeRecommendation[]> {
    try {
      log.info('Attempting to get shoe recommendations from Supabase', 'SupabaseService', params);

      // Check if shoe database tables exist by trying to fetch from them
      const [brandsResult, categoriesResult, modelsResult, mappingResult] = await Promise.all([
        supabase.from('shoe_brands').select('*').limit(1),
        supabase.from('shoe_categories').select('*').limit(1),
        supabase.from('shoe_models').select('*').limit(1),
        supabase.from('brand_category_mapping').select('*').limit(1),
      ]);

      // Check if any of the required tables are missing or have errors
      const hasErrors = brandsResult.error || categoriesResult.error || modelsResult.error || mappingResult.error;

      if (hasErrors) {
        log.error('Shoe database tables not available in Supabase', 'SupabaseService', {
          brandsError: brandsResult.error?.message,
          categoriesError: categoriesResult.error?.message,
          modelsError: modelsResult.error?.message,
          mappingError: mappingResult.error?.message,
        });

        // Return empty array instead of mock data
        log.info('Returning empty recommendations due to missing shoe database', 'SupabaseService');
        return [];
      }

      const brands = brandsResult.data || [];
      const categories = categoriesResult.data || [];
      const models = modelsResult.data || [];
      const mappings = mappingResult.data || [];

      // Calculate UK size from foot length
      const ukSize = this.footLengthToUK(params.foot_length);
      const sizeRange = { min: ukSize - 0.5, max: ukSize + 0.5 };

      // Filter shoes by size availability with null checks
      let availableShoes = models.filter(shoe => {
        // Check if shoe and size_range exist
        if (!shoe || !shoe.size_range) {
          log.warn('Shoe model missing size_range data', 'SupabaseService', {
            shoeId: shoe?.id,
            shoeName: shoe?.name
          });
          return false;
        }

        // Check if required size properties exist
        if (typeof shoe.size_range.uk_min !== 'number' || typeof shoe.size_range.uk_max !== 'number') {
          log.warn('Shoe model has invalid size_range data', 'SupabaseService', {
            shoeId: shoe.id,
            shoeName: shoe.name,
            sizeRange: shoe.size_range
          });
          return false;
        }

        return shoe.size_range.uk_min <= sizeRange.max &&
               shoe.size_range.uk_max >= sizeRange.min;
      });

      // Apply user preferences if provided
      if (params.user_preferences?.preferred_brands?.length > 0) {
        const preferredBrandIds = brands
          .filter(brand => params.user_preferences.preferred_brands.includes(brand.name.toLowerCase()))
          .map(brand => brand.id);

        if (preferredBrandIds.length > 0) {
          availableShoes = availableShoes.filter(shoe => preferredBrandIds.includes(shoe.brand_id));
        }
      }

      // Sort by relevance (price, brand popularity, etc.)
      availableShoes.sort((a, b) => {
        // Prefer shoes closer to the calculated size with null checks
        if (!a.size_range || !b.size_range) {
          return 0; // Keep original order if size data is missing
        }

        const aFit = Math.abs((a.size_range.uk_min + a.size_range.uk_max) / 2 - ukSize);
        const bFit = Math.abs((b.size_range.uk_min + b.size_range.uk_max) / 2 - ukSize);
        return aFit - bFit;
      });

      // Convert to recommendation format with additional validation
      const recommendations: MockAIShoeRecommendation[] = availableShoes.slice(0, 10).map(shoe => {
        try {
          const brand = brands.find(b => b.id === shoe.brand_id);
          const shoeCategories = mappings
            .filter(m => m.brand_id === shoe.brand_id)
            .map(m => categories.find(c => c.id === m.category_id))
            .filter(Boolean);

          return {
            brand: brand?.name || 'Unknown',
            model: shoe.name || 'Unknown Model',
            size_uk: ukSize.toString(),
            size_us: this.ukToUs(ukSize).toString(),
            size_eu: this.ukToEu(ukSize).toString(),
            confidence: 0.85,
            fit_type: params.foot_width > 10 ? 'wide' : params.foot_width < 9 ? 'narrow' : 'regular',
            category: shoeCategories[0]?.name || 'General',
            image_url: shoe.image_url || '',
          };
        } catch (mappingError) {
          log.warn('Error mapping shoe to recommendation format', 'SupabaseService', {
            shoeId: shoe.id,
            error: mappingError
          });
          // Return a basic recommendation as fallback
          return {
            brand: 'Unknown',
            model: shoe.name || 'Unknown Model',
            size_uk: ukSize.toString(),
            size_us: this.ukToUs(ukSize).toString(),
            size_eu: this.ukToEu(ukSize).toString(),
            confidence: 0.75,
            fit_type: 'regular',
            category: 'General',
            image_url: '',
          };
        }
      });

      if (recommendations.length === 0) {
        log.warn('No shoe recommendations generated from Supabase data', 'SupabaseService', {
          totalModels: models.length,
          availableShoes: availableShoes.length,
          ukSize,
          sizeRange
        });
      } else {
        log.info(`Generated ${recommendations.length} recommendations from Supabase`, 'SupabaseService');
      }

      return recommendations;

    } catch (error) {
      log.error('Error getting recommendations from Supabase', 'SupabaseService', error);
      return [];
    }
  }

  static async getBrands(): Promise<SupabaseBrand[]> {
    try {
      const { data, error } = await supabase.from('shoe_brands').select('*');
      if (error) {
        log.error('Error fetching brands from Supabase', 'SupabaseService', error);
        return [];
      }
      return data || [];
    } catch (error) {
      log.error('Error fetching brands from Supabase', 'SupabaseService', error);
      return [];
    }
  }

  static async getCategories(): Promise<SupabaseCategory[]> {
    try {
      const { data, error } = await supabase.from('shoe_categories').select('*');
      if (error) {
        log.error('Error fetching categories from Supabase', 'SupabaseService', error);
        return [];
      }
      return data || [];
    } catch (error) {
      log.error('Error fetching categories from Supabase', 'SupabaseService', error);
      return [];
    }
  }

  // Size conversion utilities
  private static footLengthToUK(lengthCm: number): number {
    return Math.round(((lengthCm - 12) / 0.847) * 2) / 2;
  }

  private static ukToUs(ukSize: number): number {
    return Math.round((ukSize + 0.5) * 2) / 2;
  }

  private static ukToEu(ukSize: number): number {
    return Math.round((ukSize + 33.5) * 2) / 2;
  }

  // Guest mode functionality
  static async saveGuestMeasurement(measurementData: {
    imageUrl: string;
    footLength: number;
    footWidth: number;
    recommendedSizeUk: string;
    recommendedSizeUs: string;
    recommendedSizeEu: string;
    confidence: number;
    recommendations: Array<{
      brand: string;
      model: string;
      sizeUk: string;
      sizeUs: string;
      sizeEu: string;
      confidence: number;
      fitType: 'narrow' | 'regular' | 'wide';
      category: string;
      imageUrl?: string;
    }>;
  }): Promise<{ success: boolean; error?: string }> {
    try {
      const guestMeasurement = {
        id: Date.now().toString(), // Simple ID for guest mode
        ...measurementData,
        created_at: new Date().toISOString(),
      };

      // Get existing guest measurements
      const existingMeasurements = await this.getGuestMeasurements();

      // Add new measurement
      const updatedMeasurements = [guestMeasurement, ...existingMeasurements];

      // Keep only last 10 measurements for guest mode
      const limitedMeasurements = updatedMeasurements.slice(0, 10);

      await AsyncStorage.setItem(GUEST_STORAGE_KEYS.MEASUREMENTS, JSON.stringify(limitedMeasurements));

      log.info('Saved guest measurement', 'SupabaseService', { id: guestMeasurement.id });
      return { success: true };
    } catch (error) {
      log.error('Error saving guest measurement', 'SupabaseService', error);
      return { success: false, error: 'Failed to save measurement locally' };
    }
  }

  static async getGuestMeasurements(): Promise<any[]> {
    try {
      const stored = await AsyncStorage.getItem(GUEST_STORAGE_KEYS.MEASUREMENTS);
      return stored ? JSON.parse(stored) : [];
    } catch (error) {
      log.error('Error getting guest measurements', 'SupabaseService', error);
      return [];
    }
  }

  static async clearGuestData(): Promise<void> {
    try {
      await Promise.all([
        AsyncStorage.removeItem(GUEST_STORAGE_KEYS.MEASUREMENTS),
        AsyncStorage.removeItem(GUEST_STORAGE_KEYS.PREFERENCES),
      ]);
      log.info('Cleared guest data', 'SupabaseService');
    } catch (error) {
      log.error('Error clearing guest data', 'SupabaseService', error);
    }
  }

  static async migrateGuestDataToUser(userId: string): Promise<{ success: boolean; error?: string }> {
    try {
      const guestMeasurements = await this.getGuestMeasurements();

      if (guestMeasurements.length === 0) {
        return { success: true };
      }

      log.info(`Migrating ${guestMeasurements.length} guest measurements to user ${userId}`, 'SupabaseService');

      // Save each guest measurement to Supabase
      for (const measurement of guestMeasurements) {
        await this.saveMeasurement(userId, {
          imageUrl: measurement.imageUrl,
          footLength: measurement.footLength,
          footWidth: measurement.footWidth,
          recommendedSizeUk: measurement.recommendedSizeUk,
          recommendedSizeUs: measurement.recommendedSizeUs,
          recommendedSizeEu: measurement.recommendedSizeEu,
          confidence: measurement.confidence,
          recommendations: measurement.recommendations,
        });
      }

      // Clear guest data after successful migration
      await this.clearGuestData();

      log.info('Successfully migrated guest data to user', 'SupabaseService');
      return { success: true };
    } catch (error) {
      log.error('Error migrating guest data to user', 'SupabaseService', error);
      return { success: false, error: 'Failed to migrate guest data' };
    }
  }

  // Utility method to check if user is in guest mode
  static isGuestMode(user: any): boolean {
    return !user;
  }

  // Unified method that works for both authenticated and guest users
  static async saveMeasurementUnified(
    user: any,
    measurementData: {
      imageUrl: string;
      footLength: number;
      footWidth: number;
      recommendedSizeUk: string;
      recommendedSizeUs: string;
      recommendedSizeEu: string;
      confidence: number;
      recommendations: Array<{
        brand: string;
        model: string;
        sizeUk: string;
        sizeUs: string;
        sizeEu: string;
        confidence: number;
        fitType: 'narrow' | 'regular' | 'wide';
        category: string;
        imageUrl?: string;
      }>;
    }
  ): Promise<{ success: boolean; measurementId?: string; error?: string }> {
    if (this.isGuestMode(user)) {
      const result = await this.saveGuestMeasurement(measurementData);
      return {
        success: result.success,
        measurementId: result.success ? Date.now().toString() : undefined,
        error: result.error,
      };
    } else {
      return await this.saveMeasurement(user.id, measurementData);
    }
  }

  static async getMeasurementsUnified(user: any): Promise<{ data: any[]; error: string | null }> {
    if (this.isGuestMode(user)) {
      const guestMeasurements = await this.getGuestMeasurements();
      return { data: guestMeasurements, error: null };
    } else {
      return await this.getMeasurements(user.id);
    }
  }
}
