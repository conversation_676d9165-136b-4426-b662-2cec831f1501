import * as Haptics from 'expo-haptics';
import { router, useLocalSearchParams } from 'expo-router';
import { useEffect, useMemo, useState } from 'react';
import { Alert, Image, ScrollView, StyleSheet, TouchableOpacity, View } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';

import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { Button } from '@/components/ui/Button';
import { Card, CardContent, CardHeader } from '@/components/ui/Card';
import { IconSymbol } from '@/components/ui/IconSymbol';
import { useAuth } from '@/contexts/AuthContext';
import { useTheme } from '@/contexts/ThemeContext';
import { ShoeCategory } from '@/services/shoeDataService';
import type { ShoeRecommendation } from '@/services/types';

export default function ResultsScreen() {
  const { colors } = useTheme();
  const { user, loading: authLoading } = useAuth();
  const { imageUri, measurementData, measurementId } = useLocalSearchParams<{
    imageUri: string;
    measurementData: string;
    measurementId?: string;
  }>();

  // History state management
  const [isSavedToHistory, setIsSavedToHistory] = useState(false);
  const [savingToHistory, setSavingToHistory] = useState(false);

  // Dynamic data state
  const [isDynamicData, setIsDynamicData] = useState(false);
  const [refreshingRecommendations, setRefreshingRecommendations] = useState(false);

  // Category filter state
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [availableCategories, setAvailableCategories] = useState<string[]>([]);

  // New state for category selection workflow
  const [showCategorySelection, setShowCategorySelection] = useState(false);
  const [selectedCategories, setSelectedCategories] = useState<string[]>([]);
  const [allCategories, setAllCategories] = useState<ShoeCategory[]>([]);
  const [generatingRecommendations, setGeneratingRecommendations] = useState(false);

  // Memoize measurement parsing to prevent re-creation on every render
  const measurement = useMemo(() => {
    try {
      return measurementData ? JSON.parse(measurementData) : null;
    } catch (error) {
      console.error('Failed to parse measurement data:', error);
      return null;
    }
  }, [measurementData]);

  // Check if we're using dynamic data and extract categories
  useEffect(() => {
    const checkDynamicData = async () => {
      try {
        const { ShoeModelService } = await import('@/services/shoeDataService');
        const models = await ShoeModelService.getFeaturedModels();
        setIsDynamicData(models.length > 0);
      } catch (error) {
        setIsDynamicData(false);
      }
    };

    // Extract unique categories from recommendations
    if (measurement?.recommendations) {
      const categories = Array.from(new Set(
        measurement.recommendations.map((rec: any) => rec.category).filter(Boolean)
      )) as string[];
      setAvailableCategories(['All Categories', ...categories]);
    }

    checkDynamicData();
  }, [measurementData]); // Use measurementData instead of measurement

  // Load all categories for selection
  useEffect(() => {
    const loadCategories = async () => {
      try {
        const { ShoeCategoryService } = await import('@/services/shoeDataService');
        const categories = await ShoeCategoryService.getCategories();
        setAllCategories(categories);

        // Check if we should show category selection (no recommendations yet)
        if (!measurement?.recommendations || measurement.recommendations.length === 0) {
          setShowCategorySelection(true);
        }
      } catch (error) {
        console.error('Failed to load categories:', error);
      }
    };

    loadCategories();
  }, [measurement]);

  // Function to refresh recommendations (for future enhancement)
  const refreshRecommendations = async () => {
    if (!measurement) return;

    setRefreshingRecommendations(true);
    try {
      // This could be enhanced to re-run the AI analysis
      // For now, just check dynamic data availability
      const { ShoeModelService } = await import('@/services/shoeDataService');
      const models = await ShoeModelService.getFeaturedModels();
      setIsDynamicData(models.length > 0);

      Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
      Alert.alert('Success', 'Recommendations refreshed!');
    } catch (error) {
      Haptics.notificationAsync(Haptics.NotificationFeedbackType.Error);
      Alert.alert('Error', 'Failed to refresh recommendations');
    } finally {
      setRefreshingRecommendations(false);
    }
  };

  // Category selection functions
  const toggleCategorySelection = (categoryName: string) => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);

    setSelectedCategories(prev => {
      if (prev.includes(categoryName)) {
        return prev.filter(c => c !== categoryName);
      } else if (prev.length < 3) {
        return [...prev, categoryName];
      } else {
        // Replace the first selected category if already at limit
        return [prev[1], prev[2], categoryName];
      }
    });
  };

  const generateRecommendations = async () => {
    if (!measurement || selectedCategories.length === 0 || !user?.id) return;

    setGeneratingRecommendations(true);
    try {
      // Use consolidated SupabaseService for recommendations
      const { SupabaseService } = await import('@/services/supabaseService');

      // Generate recommendations using real AI measurements
      const recommendations = await SupabaseService.getRecommendations({
        foot_length: measurement.foot_length,
        foot_width: measurement.foot_width,
        user_preferences: {
          preferred_categories: selectedCategories,
          preferred_brands: [], // Use empty array for now
          preferred_fit: 'regular',
        },
      });

      if (recommendations && recommendations.length > 0) {
        // Update measurement with new recommendations
        const updatedMeasurement = {
          foot_length: measurement.foot_length,
          foot_width: measurement.foot_width,
          recommended_size_uk: measurement.recommended_size_uk,
          recommended_size_us: measurement.recommended_size_us,
          recommended_size_eu: measurement.recommended_size_eu,
          confidence: measurement.confidence,
          recommendations: recommendations
        };

        // Update the measurement data
        router.setParams({
          measurementData: JSON.stringify(updatedMeasurement)
        });

        setShowCategorySelection(false);

        // Show success message
        const message = `Generated ${recommendations.length} personalized recommendations!`;

        Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
        Alert.alert('Success', message);
      } else {
        // Handle case where no recommendations are found
        console.warn('No recommendations found for selected categories', {
          selectedCategories,
          footLength: measurement.foot_length,
          footWidth: measurement.foot_width
        });

        Haptics.notificationAsync(Haptics.NotificationFeedbackType.Warning);
        Alert.alert(
          'No Recommendations Found',
          'We couldn\'t find shoes matching your selected categories. Try selecting different categories or check your internet connection.',
          [
            { text: 'Try Again', onPress: () => setShowCategorySelection(true) },
            { text: 'Continue', style: 'cancel' }
          ]
        );
      }
    } catch (error) {
      console.error('Failed to generate recommendations:', error);

      // Provide more specific error messages
      let errorMessage = 'Failed to generate recommendations. Please try again.';
      let errorTitle = 'Error';

      if (error instanceof Error) {
        if (error.message.includes('timeout') || error.message.includes('network')) {
          errorTitle = 'Connection Error';
          errorMessage = 'Unable to connect to our recommendation service. Please check your internet connection and try again.';
        } else if (error.message.includes('No recommendations found')) {
          errorTitle = 'No Results';
          errorMessage = 'We couldn\'t find any shoes matching your criteria. Try selecting different categories.';
        }
      }

      Haptics.notificationAsync(Haptics.NotificationFeedbackType.Error);
      Alert.alert(
        errorTitle,
        errorMessage,
        [
          { text: 'Retry', onPress: () => generateRecommendations() },
          { text: 'Cancel', style: 'cancel' }
        ]
      );
    } finally {
      setGeneratingRecommendations(false);
    }
  };

  // Helper function to get category icon
  const getCategoryIcon = (iconName?: string) => {
    switch (iconName) {
      case 'sports': return 'figure.run';
      case 'mountain': return 'mountain.2.fill';
      case 'casual': return 'person.fill';
      case 'formal': return 'briefcase.fill';
      case 'comfort': return 'heart.fill';
      default: return 'tag.fill';
    }
  };

  // Check if measurement is already saved to history
  useEffect(() => {
    const checkHistoryStatus = async () => {
      if (measurementId) {
        try {
          // Check if this measurement ID exists in Supabase history
          const { SupabaseService } = await import('@/services/supabaseService');
          const measurementsResult = await SupabaseService.getMeasurements(user?.id || '');
          const existsInHistory = measurementsResult.data?.some((m: any) => m.id === measurementId) || false;
          setIsSavedToHistory(existsInHistory);
        } catch (error) {
          const { log } = await import('@/utils/logger');
          log.warn('Error checking history status', 'ResultsScreen', error);
          setIsSavedToHistory(false);
        }
      }
    };

    if (user) {
      checkHistoryStatus();
    }
  }, [measurementId, user]);

  const handleScanAgain = () => {
    router.push('/upload');
  };

  const handleBackToHome = () => {
    router.push('/(tabs)' as any);
  };

  // Save to history functionality
  const saveToHistory = async () => {
    if (!user) {
      Alert.alert(
        'Authentication Required',
        'Please sign in to save to history.',
        [
          { text: 'Cancel' },
          { text: 'Sign In', onPress: () => router.replace('/auth/login') }
        ]
      );
      return;
    }

    if (!measurement) {
      Alert.alert(
        'Save Error',
        'No measurement data available. Please try scanning again.',
        [{ text: 'OK' }]
      );
      return;
    }

    try {
      setSavingToHistory(true);
      await Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);

      // Save measurement to Supabase history
      const { SupabaseService } = await import('@/services/supabaseService');

      const saveResult = await SupabaseService.saveMeasurement(user.id, {
        imageUrl: imageUri,
        footLength: measurement.foot_length,
        footWidth: measurement.foot_width,
        recommendedSizeUk: measurement.recommended_size_uk,
        recommendedSizeUs: measurement.recommended_size_us,
        recommendedSizeEu: measurement.recommended_size_eu,
        confidence: measurement.confidence,
        recommendations: measurement.recommendations.map((rec: any) => ({
          brand: rec.brand,
          model: rec.model,
          sizeUk: rec.size_uk || rec.sizeUk,
          sizeUs: rec.size_us || rec.sizeUs,
          sizeEu: rec.size_eu || rec.sizeEu,
          confidence: rec.confidence,
          fitType: rec.fit_type || rec.fitType,
          category: rec.category,
          imageUrl: rec.image_url || rec.imageUrl,
        })),
      });

      if (!saveResult.success) {
        throw new Error(saveResult.error || 'Failed to save measurement');
      }

      setIsSavedToHistory(true);
      await Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);

      Alert.alert(
        'Saved to History',
        'This measurement has been saved to your history.',
        [{ text: 'OK' }]
      );

    } catch (error) {
      const { log } = await import('@/utils/logger');
      log.error('Failed to save to history', 'ResultsScreen', error);
      await Haptics.notificationAsync(Haptics.NotificationFeedbackType.Error);
      Alert.alert(
        'Save Error',
        'Failed to save to history. Please try again.',
        [{ text: 'OK' }]
      );
    } finally {
      setSavingToHistory(false);
    }
  };



  const renderMeasurementCard = () => (
    <Card variant="outlined" padding="large">
      <CardHeader>
        <View style={styles.cardHeader}>
          <IconSymbol size={24} name="ruler.fill" color={colors.primary} />
          <ThemedText variant="h4">Your Measurements</ThemedText>
        </View>
      </CardHeader>
      <CardContent>
        <View style={styles.measurementGrid}>
          <View style={styles.measurementItem}>
            <ThemedText variant="caption" color="secondary">Length</ThemedText>
            <ThemedText variant="h3" color="primary">{measurement?.foot_length} cm</ThemedText>
          </View>
          <View style={styles.measurementItem}>
            <ThemedText variant="caption" color="secondary">Width</ThemedText>
            <ThemedText variant="h3" color="primary">{measurement?.foot_width} cm</ThemedText>
          </View>
        </View>
        
        <View style={styles.confidenceContainer}>
          <IconSymbol size={16} name="checkmark.seal.fill" color={colors.success} />
          <ThemedText variant="caption" color="secondary">
            {Math.round((measurement?.confidence || 0) * 100)}% confidence
          </ThemedText>
        </View>
      </CardContent>
    </Card>
  );

  const renderSizeCard = () => (
    <Card variant="outlined" padding="large">
      <CardHeader>
        <View style={styles.cardHeader}>
          <IconSymbol size={24} name="tag.fill" color={colors.primary} />
          <ThemedText variant="h4">Recommended Size</ThemedText>
        </View>
      </CardHeader>
      <CardContent>
        <View style={styles.sizeGrid}>
          <View style={styles.sizeItem}>
            <ThemedText variant="caption" color="secondary">UK</ThemedText>
            <ThemedText variant="h2" color="primary">{measurement?.recommended_size_uk}</ThemedText>
          </View>
          <View style={styles.sizeItem}>
            <ThemedText variant="caption" color="secondary">US</ThemedText>
            <ThemedText variant="h2" color="primary">{measurement?.recommended_size_us}</ThemedText>
          </View>
          <View style={styles.sizeItem}>
            <ThemedText variant="caption" color="secondary">EU</ThemedText>
            <ThemedText variant="h2" color="primary">{measurement?.recommended_size_eu}</ThemedText>
          </View>
        </View>
      </CardContent>
    </Card>
  );

  // Filter recommendations by selected category
  const getFilteredRecommendations = (): ShoeRecommendation[] => {
    if (!measurement?.recommendations) return [];

    if (selectedCategory === 'all' || selectedCategory === 'All Categories') {
      return measurement.recommendations;
    }

    return measurement.recommendations.filter((rec: any) =>
      rec.category?.toLowerCase() === selectedCategory.toLowerCase()
    );
  };

  // Ranking helper functions for 4 recommendations
  const getRankingLabel = (index: number): string => {
    switch (index) {
      case 0: return '1st Choice';
      case 1: return '2nd Choice';
      case 2: return '3rd Choice';
      case 3: return 'Honourable Mention';
      default: return `${index + 1}th Choice`;
    }
  };

  const getRankingColor = (index: number): string => {
    switch (index) {
      case 0: return '#FFD700'; // Gold
      case 1: return '#C0C0C0'; // Silver
      case 2: return '#CD7F32'; // Bronze
      case 3: return colors.primary; // App primary color
      default: return colors.textSecondary;
    }
  };

  const renderShoeRecommendation = (shoe: ShoeRecommendation, index: number) => (
    <Card key={index} variant="outlined" padding="medium">
      <CardContent>
        <View style={styles.shoeCard}>
          {/* Ranking Badge */}
          <View style={[styles.rankingBadge, { backgroundColor: getRankingColor(index) }]}>
            <ThemedText variant="caption" color="inverse" style={styles.rankingText}>
              {getRankingLabel(index)}
            </ThemedText>
          </View>

          <Image
            source={{ uri: shoe.image_url }}
            style={[styles.shoeImage, { backgroundColor: colors.backgroundSecondary }]}
            resizeMode="cover"
          />
          <View style={styles.shoeInfo}>
            <ThemedText variant="labelLarge">{shoe.brand}</ThemedText>
            <ThemedText variant="body">{shoe.model}</ThemedText>
            <ThemedText variant="caption" color="secondary">{shoe.category}</ThemedText>
            
            <View style={styles.shoeSizes}>
              <ThemedText variant="caption" color="secondary">
                UK {shoe.size_uk} • US {shoe.size_us} • EU {shoe.size_eu}
              </ThemedText>
            </View>
            
            <View style={styles.shoeMetadata}>
              <View style={styles.fitBadge}>
                <ThemedText variant="caption" color="primary">
                  {shoe.fit_type} fit
                </ThemedText>
              </View>
              <View style={styles.confidenceBadge}>
                <IconSymbol size={12} name="star.fill" color={colors.warning} />
                <ThemedText variant="caption" color="secondary">
                  {Math.round(shoe.confidence * 100)}%
                </ThemedText>
              </View>
            </View>
          </View>
        </View>
      </CardContent>
    </Card>
  );

  // Handle authentication state changes
  useEffect(() => {
    if (!authLoading && !user) {
      // User is not authenticated, but allow viewing results
      const logUserState = async () => {
        const { log } = await import('@/utils/logger');
        log.debug('User not authenticated, but allowing results view', 'ResultsScreen');
      };
      logUserState();
    }
  }, [user, authLoading]);

  if (!measurement) {
    return (
      <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
        <View style={styles.errorContainer}>
          <IconSymbol size={64} name="exclamationmark.triangle" color={colors.error} />
          <ThemedText variant="h3" color="error">No Results Found</ThemedText>
          <ThemedText variant="body" color="secondary" style={styles.errorText}>
            Unable to load measurement results. Please try again.
          </ThemedText>
          <Button title="Go Back" onPress={handleBackToHome} variant="primary" />
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
      {/* Header */}
      <ThemedView style={styles.header}>
        <View style={styles.headerContent}>
          <IconSymbol size={32} name="checkmark.circle.fill" color={colors.success} />
          <ThemedText variant="h2">Analysis Complete!</ThemedText>
        </View>

        {/* History Button */}
        <TouchableOpacity
          style={[styles.historyButton, { backgroundColor: colors.backgroundSecondary }]}
          onPress={saveToHistory}
          disabled={savingToHistory || isSavedToHistory}
          activeOpacity={0.7}
        >
          <IconSymbol
            size={24}
            name={isSavedToHistory ? "checkmark.circle.fill" : "clock"}
            color={isSavedToHistory ? colors.success : colors.iconSecondary}
          />
        </TouchableOpacity>
      </ThemedView>

      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        {/* Image Preview */}
        {imageUri && (
          <ThemedView style={styles.section}>
            <View style={styles.imageContainer}>
              <Image 
                source={{ uri: imageUri }} 
                style={[styles.image, { borderColor: colors.border }]}
                resizeMode="cover"
              />
            </View>
          </ThemedView>
        )}

        {/* Measurements */}
        <ThemedView style={styles.section}>
          {renderMeasurementCard()}
        </ThemedView>

        {/* Recommended Size */}
        <ThemedView style={styles.section}>
          {renderSizeCard()}
        </ThemedView>

        {/* Category Selection */}
        {showCategorySelection && (
          <ThemedView style={styles.section}>
            <Card variant="outlined" padding="large">
              <CardHeader>
                <View style={styles.cardHeader}>
                  <IconSymbol size={24} name="tag.fill" color={colors.primary} />
                  <ThemedText variant="h4">Select Shoe Categories</ThemedText>
                </View>
                <ThemedText variant="body" color="secondary" style={styles.categoryDescription}>
                  Choose up to 3 categories to get personalized shoe recommendations based on your foot measurements.
                </ThemedText>
              </CardHeader>
              <CardContent>
                <View style={styles.categoryGrid}>
                  {allCategories.map((category) => (
                    <TouchableOpacity
                      key={category.id}
                      style={[
                        styles.categoryCard,
                        {
                          backgroundColor: selectedCategories.includes(category.name)
                            ? colors.primary
                            : colors.backgroundSecondary,
                          borderColor: selectedCategories.includes(category.name)
                            ? colors.primary
                            : colors.border,
                        }
                      ]}
                      onPress={() => toggleCategorySelection(category.name)}
                      activeOpacity={0.7}
                    >
                      <View style={styles.categoryCardContent}>
                        <View style={[
                          styles.categoryIcon,
                          { backgroundColor: category.color_theme || colors.primary }
                        ]}>
                          <IconSymbol
                            size={20}
                            name={getCategoryIcon(category.icon_name)}
                            color="white"
                          />
                        </View>
                        <ThemedText
                          variant="labelLarge"
                          color={selectedCategories.includes(category.name) ? 'inverse' : 'primary'}
                          style={styles.categoryName}
                        >
                          {category.name}
                        </ThemedText>
                        <ThemedText
                          variant="caption"
                          color={selectedCategories.includes(category.name) ? 'inverse' : 'secondary'}
                          style={styles.categoryDescription}
                        >
                          {category.description}
                        </ThemedText>
                      </View>
                      {selectedCategories.includes(category.name) && (
                        <View style={styles.selectedBadge}>
                          <IconSymbol size={16} name="checkmark" color="white" />
                        </View>
                      )}
                    </TouchableOpacity>
                  ))}
                </View>

                <View style={styles.categorySelectionFooter}>
                  <ThemedText variant="caption" color="secondary">
                    {selectedCategories.length}/3 categories selected
                  </ThemedText>

                  <Button
                    title={generatingRecommendations ? "Generating..." : "Get Recommendations"}
                    onPress={generateRecommendations}
                    disabled={selectedCategories.length === 0 || generatingRecommendations}
                    variant="primary"
                    style={styles.generateButton}
                  />
                </View>
              </CardContent>
            </Card>
          </ThemedView>
        )}

        {/* Shoe Recommendations */}
        <ThemedView style={styles.section}>
          <View style={styles.recommendationsHeader}>
            <ThemedText variant="h4" style={styles.sectionTitle}>
              Recommended Shoes
            </ThemedText>
            <View style={styles.dataSourceIndicator}>
              <View style={[styles.statusDot, { backgroundColor: isDynamicData ? '#4CAF50' : '#FF9500' }]} />
              <ThemedText variant="caption" color="secondary">
                {isDynamicData ? 'Live Database' : 'Fallback Data'}
              </ThemedText>
              <TouchableOpacity
                onPress={refreshRecommendations}
                disabled={refreshingRecommendations}
                style={styles.refreshButton}
              >
                <IconSymbol
                  name={refreshingRecommendations ? "arrow.clockwise" : "arrow.clockwise"}
                  size={16}
                  color={colors.textSecondary}
                />
              </TouchableOpacity>
            </View>
          </View>

          {/* Category Filter */}
          {availableCategories.length > 1 && (
            <View style={styles.categoryFilter}>
              <ThemedText variant="labelLarge" style={styles.filterLabel}>
                Filter by Category:
              </ThemedText>
              <ScrollView
                horizontal
                showsHorizontalScrollIndicator={false}
                style={styles.categoryScrollView}
              >
                {availableCategories.map((category) => (
                  <TouchableOpacity
                    key={category}
                    style={[
                      styles.categoryChip,
                      {
                        backgroundColor: selectedCategory === category || (selectedCategory === 'all' && category === 'All Categories')
                          ? colors.primary
                          : colors.backgroundSecondary,
                        borderColor: selectedCategory === category || (selectedCategory === 'all' && category === 'All Categories')
                          ? colors.primary
                          : colors.border,
                      }
                    ]}
                    onPress={() => {
                      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
                      setSelectedCategory(category === 'All Categories' ? 'all' : category);
                    }}
                    activeOpacity={0.7}
                  >
                    <ThemedText
                      variant="caption"
                      color={selectedCategory === category || (selectedCategory === 'all' && category === 'All Categories') ? 'inverse' : 'primary'}
                    >
                      {category}
                    </ThemedText>
                  </TouchableOpacity>
                ))}
              </ScrollView>
            </View>
          )}

          <View style={styles.recommendationsList}>
            {getFilteredRecommendations().map((shoe: ShoeRecommendation, index: number) =>
              renderShoeRecommendation(shoe, index)
            )}
          </View>
        </ThemedView>

        {/* Action Buttons */}
        <ThemedView style={styles.section}>
          <View style={styles.actionButtons}>
            <Button
              title={isSavedToHistory ? "Saved to History" : "Save to History"}
              onPress={saveToHistory}
              variant={isSavedToHistory ? "secondary" : "primary"}
              size="large"
              icon={isSavedToHistory ? "checkmark.circle.fill" : "clock"}
              fullWidth
              disabled={savingToHistory || isSavedToHistory}
            />
            <View style={styles.secondaryButtons}>
              <Button
                title="Scan Again"
                onPress={handleScanAgain}
                variant="outline"
                size="medium"
                icon="camera.fill"
                style={styles.secondaryButton}
              />
              <Button
                title="Back to Home"
                onPress={handleBackToHome}
                variant="secondary"
                size="medium"
                icon="house.fill"
                style={styles.secondaryButton}
              />
            </View>
          </View>
        </ThemedView>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 20,
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  historyButton: {
    width: 48,
    height: 48,
    borderRadius: 24,
    alignItems: 'center',
    justifyContent: 'center',
  },
  scrollView: {
    flex: 1,
  },
  section: {
    paddingHorizontal: 20,
    paddingBottom: 24,
  },
  sectionTitle: {
    marginBottom: 16,
  },
  imageContainer: {
    alignItems: 'center',
  },
  image: {
    width: 150,
    height: 150,
    borderRadius: 12,
    borderWidth: 2,
  },
  cardHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  measurementGrid: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginBottom: 16,
  },
  measurementItem: {
    alignItems: 'center',
    gap: 4,
  },
  confidenceContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    gap: 4,
  },
  sizeGrid: {
    flexDirection: 'row',
    justifyContent: 'space-around',
  },
  sizeItem: {
    alignItems: 'center',
    gap: 4,
  },
  recommendationsList: {
    gap: 16,
  },
  shoeCard: {
    flexDirection: 'row',
    gap: 12,
  },
  shoeImage: {
    width: 80,
    height: 80,
    borderRadius: 8,
  },
  shoeInfo: {
    flex: 1,
    gap: 4,
  },
  shoeSizes: {
    marginTop: 4,
  },
  shoeMetadata: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
    marginTop: 4,
  },
  fitBadge: {
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 4,
    backgroundColor: '#00C85120',
  },
  confidenceBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 2,
  },
  actionButtons: {
    gap: 16,
  },
  secondaryButtons: {
    flexDirection: 'row',
    gap: 12,
  },
  secondaryButton: {
    flex: 1,
  },
  errorContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    padding: 40,
    gap: 16,
  },
  errorText: {
    textAlign: 'center',
    maxWidth: 280,
  },
  recommendationsHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  dataSourceIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
  },
  statusDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
  },
  refreshButton: {
    padding: 4,
    marginLeft: 4,
  },
  // Ranking badge styles
  rankingBadge: {
    position: 'absolute',
    top: 8,
    left: 8,
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    zIndex: 1,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  rankingText: {
    fontSize: 10,
    fontWeight: '600',
  },
  // Category filter styles
  categoryFilter: {
    marginBottom: 16,
  },
  filterLabel: {
    marginBottom: 8,
  },
  categoryScrollView: {
    flexGrow: 0,
  },
  categoryChip: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    borderWidth: 1,
    marginRight: 8,
  },
  // Category selection styles
  categoryDescription: {
    marginTop: 8,
    textAlign: 'center',
  },
  categoryGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
    marginBottom: 20,
  },
  categoryCard: {
    flex: 1,
    minWidth: '45%',
    maxWidth: '48%',
    borderRadius: 12,
    borderWidth: 2,
    padding: 16,
    position: 'relative',
  },
  categoryCardContent: {
    alignItems: 'center',
    gap: 8,
  },
  categoryIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
  },
  categoryName: {
    textAlign: 'center',
    fontWeight: '600',
  },
  selectedBadge: {
    position: 'absolute',
    top: 8,
    right: 8,
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
    alignItems: 'center',
    justifyContent: 'center',
  },
  categorySelectionFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: 16,
  },
  generateButton: {
    minWidth: 150,
  },
});
