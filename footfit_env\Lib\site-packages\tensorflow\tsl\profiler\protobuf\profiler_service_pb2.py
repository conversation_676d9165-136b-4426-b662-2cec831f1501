# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: tensorflow/tsl/profiler/protobuf/profiler_service.proto
"""Generated protocol buffer code."""
from google.protobuf.internal import builder as _builder
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from tensorflow.tsl.profiler.protobuf import profiler_options_pb2 as tensorflow_dot_tsl_dot_profiler_dot_protobuf_dot_profiler__options__pb2
from tensorflow.tsl.profiler.protobuf import profiler_service_monitor_result_pb2 as tensorflow_dot_tsl_dot_profiler_dot_protobuf_dot_profiler__service__monitor__result__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n7tensorflow/tsl/profiler/protobuf/profiler_service.proto\x12\ntensorflow\x1a\x37tensorflow/tsl/profiler/protobuf/profiler_options.proto\x1a\x46tensorflow/tsl/profiler/protobuf/profiler_service_monitor_result.proto\"B\n\x12ToolRequestOptions\x12\x16\n\x0eoutput_formats\x18\x02 \x01(\t\x12\x14\n\x0csave_to_repo\x18\x03 \x01(\x08\"\xc9\x02\n\x0eProfileRequest\x12\x13\n\x0b\x64uration_ms\x18\x01 \x01(\x04\x12\x12\n\nmax_events\x18\x02 \x01(\x04\x12\r\n\x05tools\x18\x03 \x03(\t\x12\x41\n\x0ctool_options\x18\x08 \x03(\x0b\x32+.tensorflow.ProfileRequest.ToolOptionsEntry\x12(\n\x04opts\x18\x04 \x01(\x0b\x32\x1a.tensorflow.ProfileOptions\x12\x17\n\x0frepository_root\x18\x05 \x01(\t\x12\x12\n\nsession_id\x18\x06 \x01(\t\x12\x11\n\thost_name\x18\x07 \x01(\t\x1aR\n\x10ToolOptionsEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12-\n\x05value\x18\x02 \x01(\x0b\x32\x1e.tensorflow.ToolRequestOptions:\x02\x38\x01\"-\n\x0fProfileToolData\x12\x0c\n\x04name\x18\x01 \x01(\t\x12\x0c\n\x04\x64\x61ta\x18\x02 \x01(\x0c\"t\n\x0fProfileResponse\x12.\n\ttool_data\x18\x06 \x03(\x0b\x32\x1b.tensorflow.ProfileToolData\x12\x13\n\x0b\x65mpty_trace\x18\x07 \x01(\x08J\x04\x08\x01\x10\x02J\x04\x08\x02\x10\x03J\x04\x08\x03\x10\x04J\x04\x08\x04\x10\x05J\x04\x08\x05\x10\x06\"&\n\x10TerminateRequest\x12\x12\n\nsession_id\x18\x01 \x01(\t\"\x13\n\x11TerminateResponse\"R\n\x0eMonitorRequest\x12\x13\n\x0b\x64uration_ms\x18\x01 \x01(\x04\x12\x18\n\x10monitoring_level\x18\x02 \x01(\x05\x12\x11\n\ttimestamp\x18\x03 \x01(\x08\"\x91\x01\n\x0fMonitorResponse\x12\x0c\n\x04\x64\x61ta\x18\x01 \x01(\t\x12@\n\x0emonitor_result\x18\n \x01(\x0b\x32(.tensorflow.ProfilerServiceMonitorResultJ\x04\x08\x02\x10\x03J\x04\x08\x03\x10\x04J\x04\x08\x04\x10\x05J\x04\x08\x05\x10\x06J\x04\x08\x06\x10\x07J\x04\x08\x07\x10\x08J\x04\x08\x08\x10\tJ\x04\x08\t\x10\n2\xe9\x01\n\x0fProfilerService\x12\x44\n\x07Profile\x12\x1a.tensorflow.ProfileRequest\x1a\x1b.tensorflow.ProfileResponse\"\x00\x12J\n\tTerminate\x12\x1c.tensorflow.TerminateRequest\x1a\x1d.tensorflow.TerminateResponse\"\x00\x12\x44\n\x07Monitor\x12\x1a.tensorflow.MonitorRequest\x1a\x1b.tensorflow.MonitorResponse\"\x00\x62\x06proto3')

_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, globals())
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'tensorflow.tsl.profiler.protobuf.profiler_service_pb2', globals())
if _descriptor._USE_C_DESCRIPTORS == False:

  DESCRIPTOR._options = None
  _PROFILEREQUEST_TOOLOPTIONSENTRY._options = None
  _PROFILEREQUEST_TOOLOPTIONSENTRY._serialized_options = b'8\001'
  _TOOLREQUESTOPTIONS._serialized_start=200
  _TOOLREQUESTOPTIONS._serialized_end=266
  _PROFILEREQUEST._serialized_start=269
  _PROFILEREQUEST._serialized_end=598
  _PROFILEREQUEST_TOOLOPTIONSENTRY._serialized_start=516
  _PROFILEREQUEST_TOOLOPTIONSENTRY._serialized_end=598
  _PROFILETOOLDATA._serialized_start=600
  _PROFILETOOLDATA._serialized_end=645
  _PROFILERESPONSE._serialized_start=647
  _PROFILERESPONSE._serialized_end=763
  _TERMINATEREQUEST._serialized_start=765
  _TERMINATEREQUEST._serialized_end=803
  _TERMINATERESPONSE._serialized_start=805
  _TERMINATERESPONSE._serialized_end=824
  _MONITORREQUEST._serialized_start=826
  _MONITORREQUEST._serialized_end=908
  _MONITORRESPONSE._serialized_start=911
  _MONITORRESPONSE._serialized_end=1056
  _PROFILERSERVICE._serialized_start=1059
  _PROFILERSERVICE._serialized_end=1292
# @@protoc_insertion_point(module_scope)
