# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: tensorflow/tsl/protobuf/status.proto
"""Generated protocol buffer code."""
from google.protobuf.internal import builder as _builder
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from tensorflow.tsl.protobuf import error_codes_pb2 as tensorflow_dot_tsl_dot_protobuf_dot_error__codes__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n$tensorflow/tsl/protobuf/status.proto\x12\ntensorflow\x1a)tensorflow/tsl/protobuf/error_codes.proto\"D\n\x0bStatusProto\x12$\n\x04\x63ode\x18\x01 \x01(\x0e\x32\x16.tensorflow.error.Code\x12\x0f\n\x07message\x18\x02 \x01(\tB_\n\x18org.tensorflow.frameworkP\x01Z>github.com/google/tsl/tsl/go/protobuf/for_core_protos_go_proto\xf8\x01\x01\x62\x06proto3')

_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, globals())
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'tensorflow.tsl.protobuf.status_pb2', globals())
if _descriptor._USE_C_DESCRIPTORS == False:

  DESCRIPTOR._options = None
  DESCRIPTOR._serialized_options = b'\n\030org.tensorflow.frameworkP\001Z>github.com/google/tsl/tsl/go/protobuf/for_core_protos_go_proto\370\001\001'
  _STATUSPROTO._serialized_start=95
  _STATUSPROTO._serialized_end=163
# @@protoc_insertion_point(module_scope)
